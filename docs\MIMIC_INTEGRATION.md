# MIMIC-IV Integration for Medical Steganography

This document provides comprehensive guidance on integrating the MIMIC-IV dataset with your SteganoGAN medical steganography project.

## Overview

MIMIC-IV (Medical Information Mart for Intensive Care IV) is a large, freely-available database comprising de-identified health-related data associated with patients who stayed in critical care units. It's perfect for your PhD research as it provides:

- **Real medical data**: Authentic patient records, diagnoses, lab results, and prescriptions
- **Large scale**: Over 40,000 patients with comprehensive medical histories
- **Research-ready**: Already anonymized and structured for academic use
- **Diverse cases**: Wide range of medical conditions and scenarios

## Prerequisites

### 1. MIMIC-IV Access

1. **Get Access**: Complete the CITI training and request access at https://mimic.mit.edu/
2. **Download Dataset**: Download MIMIC-IV v2.2 (latest version)
3. **Verify Files**: Ensure you have the core tables (patients, admissions, diagnoses_icd, etc.)

### 2. System Requirements

```bash
# Install additional dependencies for MIMIC-IV
pip install sqlalchemy psycopg2-binary pyarrow fastparquet
```

## Dataset Structure

MIMIC-IV is organized into several modules:

```
mimic-iv/
├── core/           # Core patient data
│   ├── patients.csv.gz
│   ├── admissions.csv.gz
│   └── transfers.csv.gz
├── hosp/           # Hospital data
│   ├── diagnoses_icd.csv.gz
│   ├── procedures_icd.csv.gz
│   ├── prescriptions.csv.gz
│   └── labevents.csv.gz
└── icu/            # ICU data
    ├── chartevents.csv.gz
    └── inputevents.csv.gz
```

## Integration Steps

### 1. Setup MIMIC-IV for Steganography

```bash
# Setup MIMIC-IV integration
python scripts/setup_mimic.py \
    --mimic_path /path/to/mimic-iv \
    --image_dir data/raw \
    --output_dir data/mimic_processed \
    --num_records 1000
```

This will:
- Validate your MIMIC-IV installation
- Extract and process medical records
- Create anonymized datasets for training
- Generate train/validation/test splits

### 2. Verify Setup

```bash
# Validate the setup
python scripts/setup_mimic.py \
    --action validate \
    --config data/mimic_processed/mimic_config.json
```

### 3. Analyze Dataset

```bash
# Get dataset statistics
python scripts/setup_mimic.py \
    --action analyze \
    --mimic_path /path/to/mimic-iv
```

## Medical Record Structure

The processed MIMIC-IV records have the following structure:

```json
{
    "record_type": "mimic_iv",
    "patient_id": "ANON_12345678",
    "timestamp": "REDACTED",
    "source": "MIMIC-IV",
    "version": "2.2",
    "gender": "F",
    "anchor_age": 65,
    "admission": {
        "admission_id": "12345",
        "admission_type": "EMERGENCY",
        "admission_location": "EMERGENCY ROOM",
        "discharge_location": "HOME",
        "insurance": "Medicare",
        "ethnicity": "WHITE"
    },
    "diagnoses": [
        {
            "icd_code": "I50.9",
            "icd_version": 10,
            "seq_num": 1
        }
    ],
    "lab_results": [
        {
            "itemid": "50912",
            "value": "15.2",
            "valuenum": 15.2,
            "valueuom": "mg/dL",
            "flag": "abnormal"
        }
    ],
    "prescriptions": [
        {
            "drug": "Lisinopril",
            "dose_val_rx": "10",
            "dose_unit_rx": "mg",
            "route": "PO"
        }
    ]
}
```

## Training with MIMIC-IV Data

### 1. Basic Training

```bash
# Train with MIMIC-IV configuration
python scripts/train.py --config mimic
```

### 2. Fast Training (for testing)

```bash
# Quick training with sample data
python scripts/train.py --config mimic_fast
```

### 3. Custom Training

```python
from config.training_config import TrainingConfig

# Create custom MIMIC configuration
config = TrainingConfig()
config.data.medical_data_path = "data/mimic_processed/train_records.json"
config.data.medical_data_ratio = 1.0  # Use medical data for all samples
config.loss.medical_data_weight = 30.0  # Higher weight for medical accuracy
```

## Demo with MIMIC-IV Data

```bash
# Run demo with MIMIC-IV medical records
python scripts/demo.py \
    --image data/raw/sample_image.jpg \
    --medical_data data/mimic_processed/sample_records.json \
    --output results/mimic_demo
```

## Research Applications

### 1. Telemedicine Scenarios

- **Emergency Consultations**: Hide critical patient data in medical images
- **Remote Monitoring**: Embed vital signs and lab results in routine scans
- **Specialist Referrals**: Include comprehensive medical history with imaging

### 2. Medical Data Types Supported

- **Patient Demographics**: Age, gender, ethnicity (anonymized)
- **Admission Details**: Type, location, insurance information
- **Diagnoses**: ICD-9/10 codes with sequence numbers
- **Laboratory Results**: Values, units, normal/abnormal flags
- **Prescriptions**: Medications, dosages, routes of administration
- **Vital Signs**: Heart rate, blood pressure, temperature
- **Clinical Notes**: Discharge summaries, progress notes

### 3. Privacy and Security

- **Automatic Anonymization**: Patient IDs are hashed and anonymized
- **Date Shifting**: Temporal information is removed or shifted
- **Size Optimization**: Records are truncated to fit embedding limits
- **HIPAA Compliance**: Built-in privacy protection measures

## Performance Optimization

### 1. Record Size Management

```python
# Configure maximum record size
processor = MIMICIVProcessor(
    mimic_path="path/to/mimic",
    max_record_size=1024,  # bytes
    anonymize=True
)
```

### 2. Selective Data Inclusion

```python
# Include only specific tables
processor = MIMICIVProcessor(
    mimic_path="path/to/mimic",
    include_tables=['patients', 'admissions', 'diagnoses_icd', 'prescriptions']
)
```

### 3. Batch Processing

```python
# Process records in batches
records = processor.get_random_patient_records(num_records=1000)
```

## Evaluation Metrics

### 1. Medical Data Integrity

- **Extraction Accuracy**: How accurately medical data is recovered
- **Critical Field Preservation**: Accuracy for essential medical information
- **Diagnosis Preservation**: Correct recovery of ICD codes
- **Prescription Accuracy**: Medication and dosage information integrity

### 2. Clinical Relevance

- **Semantic Consistency**: Medical meaning preservation
- **Temporal Relationships**: Chronological data integrity
- **Cross-Reference Validity**: Consistency between related fields

## Troubleshooting

### Common Issues

1. **Missing Tables**
   ```bash
   # Check which tables are available
   python scripts/setup_mimic.py --action analyze --mimic_path /path/to/mimic
   ```

2. **Large Record Sizes**
   ```python
   # Reduce record size by limiting components
   processor.max_record_size = 512  # Smaller limit
   ```

3. **Memory Issues**
   ```python
   # Process smaller batches
   records = processor.get_random_patient_records(num_records=100)
   ```

### Performance Tips

1. **Use Parquet Format**: Convert CSV files to Parquet for faster loading
2. **Index Tables**: Create database indexes for frequently accessed columns
3. **Cache Processed Data**: Save processed records to avoid reprocessing
4. **Parallel Processing**: Use multiple workers for data loading

## Research Contributions

Using MIMIC-IV in your steganography research provides several advantages:

### 1. **Authenticity**
- Real medical data increases research validity
- Diverse patient populations and conditions
- Realistic data distributions and correlations

### 2. **Scale**
- Large dataset enables robust training
- Sufficient data for train/validation/test splits
- Multiple medical scenarios and edge cases

### 3. **Reproducibility**
- Standardized dataset used by medical AI community
- Consistent data format and structure
- Enables comparison with other research

### 4. **Clinical Relevance**
- Addresses real-world telemedicine challenges
- Supports practical deployment scenarios
- Validates system with authentic medical workflows

## Next Steps

1. **Obtain MIMIC-IV Access**: Complete the required training and access request
2. **Setup Integration**: Run the setup scripts to process your data
3. **Validate System**: Test with sample records before full training
4. **Train Models**: Use MIMIC-IV data for comprehensive training
5. **Evaluate Performance**: Assess both technical and medical metrics
6. **Document Results**: Prepare findings for academic publication

## Citation

When using MIMIC-IV in your research, please cite:

```bibtex
@article{johnson2023mimic,
    title={MIMIC-IV, a freely accessible electronic health record dataset},
    author={Johnson, Alistair EW and Bulgarelli, Lucas and Shen, Lu and others},
    journal={Scientific Data},
    volume={10},
    number={1},
    pages={1--9},
    year={2023},
    publisher={Nature Publishing Group}
}
```

## Support

For MIMIC-IV specific issues:
- MIMIC Documentation: https://mimic.mit.edu/docs/iv/
- MIMIC Forums: https://github.com/MIT-LCP/mimic-code/discussions

For integration issues:
- Check the setup logs in `data/mimic_processed/`
- Validate your MIMIC-IV installation
- Ensure all required tables are present

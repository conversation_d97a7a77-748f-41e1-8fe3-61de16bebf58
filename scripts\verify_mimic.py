#!/usr/bin/env python3
"""
Verification script for MIMIC-IV dataset integrity and completeness.
"""

import os
import sys
import gzip
import pandas as pd
from pathlib import Path
import hashlib
import argparse


def check_file_integrity(file_path: Path) -> dict:
    """Check if a file exists and can be read."""
    result = {
        'exists': file_path.exists(),
        'readable': False,
        'size_mb': 0,
        'rows': 0,
        'columns': 0,
        'error': None
    }
    
    if result['exists']:
        try:
            # Get file size
            result['size_mb'] = file_path.stat().st_size / (1024 * 1024)
            
            # Try to read first few rows
            if file_path.suffix == '.gz':
                df = pd.read_csv(file_path, nrows=100)
            else:
                df = pd.read_csv(file_path, nrows=100)
            
            result['readable'] = True
            result['columns'] = len(df.columns)
            
            # Get total row count (approximate for large files)
            if result['size_mb'] < 100:  # Only count rows for smaller files
                if file_path.suffix == '.gz':
                    full_df = pd.read_csv(file_path)
                else:
                    full_df = pd.read_csv(file_path)
                result['rows'] = len(full_df)
            else:
                result['rows'] = 'Large file - not counted'
                
        except Exception as e:
            result['error'] = str(e)
    
    return result


def verify_mimic_installation(mimic_path: str) -> dict:
    """Verify MIMIC-IV installation completeness."""
    mimic_path = Path(mimic_path)
    
    # Expected files with approximate sizes (MB)
    expected_files = {
        'core/patients.csv.gz': {'min_size': 1, 'max_size': 5, 'required': True},
        'core/admissions.csv.gz': {'min_size': 30, 'max_size': 70, 'required': True},
        'core/transfers.csv.gz': {'min_size': 10, 'max_size': 30, 'required': False},
        'hosp/diagnoses_icd.csv.gz': {'min_size': 80, 'max_size': 120, 'required': True},
        'hosp/prescriptions.csv.gz': {'min_size': 150, 'max_size': 250, 'required': True},
        'hosp/labevents.csv.gz': {'min_size': 4000, 'max_size': 6000, 'required': True},
        'hosp/procedures_icd.csv.gz': {'min_size': 30, 'max_size': 70, 'required': False},
        'hosp/microbiologyevents.csv.gz': {'min_size': 15, 'max_size': 30, 'required': False},
        'icu/chartevents.csv.gz': {'min_size': 15000, 'max_size': 25000, 'required': False},
        'icu/inputevents.csv.gz': {'min_size': 500, 'max_size': 1000, 'required': False},
    }
    
    verification_results = {
        'total_files': len(expected_files),
        'found_files': 0,
        'required_files': 0,
        'required_found': 0,
        'file_details': {},
        'overall_status': 'UNKNOWN'
    }
    
    print(f"Verifying MIMIC-IV installation at: {mimic_path}")
    print("=" * 80)
    
    for file_path, expected in expected_files.items():
        full_path = mimic_path / file_path
        result = check_file_integrity(full_path)
        
        # Check size expectations
        size_ok = True
        if result['readable'] and result['size_mb'] > 0:
            if (result['size_mb'] < expected['min_size'] or 
                result['size_mb'] > expected['max_size']):
                size_ok = False
                result['size_warning'] = f"Size {result['size_mb']:.1f}MB outside expected range {expected['min_size']}-{expected['max_size']}MB"
        
        result['required'] = expected['required']
        result['size_ok'] = size_ok
        verification_results['file_details'][file_path] = result
        
        # Count statistics
        if result['exists']:
            verification_results['found_files'] += 1
        
        if expected['required']:
            verification_results['required_files'] += 1
            if result['exists'] and result['readable']:
                verification_results['required_found'] += 1
        
        # Print status
        status = "✓" if result['exists'] and result['readable'] and size_ok else "✗"
        required_marker = "[REQUIRED]" if expected['required'] else "[OPTIONAL]"
        size_info = f"{result['size_mb']:.1f}MB" if result['size_mb'] > 0 else "N/A"
        
        print(f"{status} {file_path:<35} {required_marker:<12} {size_info:<10}")
        
        if result['error']:
            print(f"    Error: {result['error']}")
        if not size_ok and 'size_warning' in result:
            print(f"    Warning: {result['size_warning']}")
    
    # Determine overall status
    if verification_results['required_found'] == verification_results['required_files']:
        verification_results['overall_status'] = 'COMPLETE'
    elif verification_results['required_found'] > 0:
        verification_results['overall_status'] = 'PARTIAL'
    else:
        verification_results['overall_status'] = 'FAILED'
    
    print("=" * 80)
    print(f"Verification Summary:")
    print(f"  Required files found: {verification_results['required_found']}/{verification_results['required_files']}")
    print(f"  Total files found: {verification_results['found_files']}/{verification_results['total_files']}")
    print(f"  Overall status: {verification_results['overall_status']}")
    
    return verification_results


def test_data_loading(mimic_path: str) -> bool:
    """Test if data can be loaded successfully."""
    mimic_path = Path(mimic_path)
    
    print("\nTesting data loading...")
    
    # Test loading patients table
    patients_path = mimic_path / 'core' / 'patients.csv.gz'
    if patients_path.exists():
        try:
            df = pd.read_csv(patients_path, nrows=10)
            print(f"✓ Successfully loaded patients table: {len(df)} rows, {len(df.columns)} columns")
            print(f"  Columns: {list(df.columns)}")
            return True
        except Exception as e:
            print(f"✗ Failed to load patients table: {e}")
            return False
    else:
        print("✗ Patients table not found")
        return False


def generate_setup_command(mimic_path: str) -> str:
    """Generate the setup command for the steganography project."""
    return f"""
# Setup command for your steganography project:
python scripts/setup_mimic.py \\
    --mimic_path "{mimic_path}" \\
    --image_dir data/raw \\
    --output_dir data/mimic_processed \\
    --num_records 1000

# Quick test with sample data:
python scripts/setup_mimic.py \\
    --mimic_path "{mimic_path}" \\
    --action sample \\
    --output_dir data/mimic_processed
"""


def main():
    parser = argparse.ArgumentParser(description='Verify MIMIC-IV dataset installation')
    parser.add_argument('mimic_path', type=str, help='Path to MIMIC-IV dataset directory')
    parser.add_argument('--test-loading', action='store_true', help='Test data loading')
    
    args = parser.parse_args()
    
    # Verify installation
    results = verify_mimic_installation(args.mimic_path)
    
    # Test loading if requested
    if args.test_loading:
        loading_success = test_data_loading(args.mimic_path)
    else:
        loading_success = True
    
    # Generate setup instructions
    if results['overall_status'] in ['COMPLETE', 'PARTIAL']:
        print(generate_setup_command(args.mimic_path))
    
    # Exit with appropriate code
    if results['overall_status'] == 'COMPLETE' and loading_success:
        print("\n🎉 MIMIC-IV verification successful! Ready for steganography setup.")
        sys.exit(0)
    elif results['overall_status'] == 'PARTIAL':
        print("\n⚠️  MIMIC-IV partially available. Some features may be limited.")
        sys.exit(1)
    else:
        print("\n❌ MIMIC-IV verification failed. Please check your installation.")
        sys.exit(2)


if __name__ == "__main__":
    main()

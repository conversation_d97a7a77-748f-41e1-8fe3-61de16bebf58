# Core Deep Learning Framework
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Neural Network and Training Utilities
pytorch-lightning>=2.0.0
torchmetrics>=0.11.0
tensorboard>=2.12.0
wandb>=0.15.0

# Image Processing and Computer Vision
opencv-python>=4.7.0
Pillow>=9.5.0
scikit-image>=0.20.0
imageio>=2.28.0
albumentations>=1.3.0

# Scientific Computing
numpy>=1.24.0
scipy>=1.10.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Data Handling and Utilities
pandas>=2.0.0
h5py>=3.8.0
tqdm>=4.65.0
pyyaml>=6.0

# Evaluation Metrics
lpips>=0.1.4
pytorch-fid>=0.3.0
# ssim-pytorch>=0.0.1  # Not available, will use scikit-image SSIM instead

# Medical Data Processing
pydicom>=2.3.0
nibabel>=5.1.0
# SimpleITK>=2.2.0  # Optional - can be installed separately if needed

# MIMIC-IV Dataset Support (Optional - install when needed)
# sqlalchemy>=2.0.0
# psycopg2-binary>=2.9.0
# pymongo>=4.3.0
# pyarrow>=12.0.0
# fastparquet>=2023.4.0

# Cryptography (for future ECC integration)
cryptography>=40.0.0
pycryptodome>=3.17.0

# Development and Testing (Optional)
# pytest>=7.3.0
# black>=23.3.0
# flake8>=6.0.0
# isort>=5.12.0

# Jupyter and Visualization (Optional)
# jupyter>=1.0.0
# ipywidgets>=8.0.0
plotly>=5.14.0

# Configuration Management (Optional)
# hydra-core>=1.3.0
# omegaconf>=2.3.0

# Memory and Performance (Optional)
# psutil>=5.9.0
# memory-profiler>=0.60.0

# File I/O and Compression (Optional)
# lz4>=4.3.0
# zstandard>=0.21.0

#!/usr/bin/env python3
"""
Setup script for MIMIC-IV dataset integration with SteganoGAN.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

import json
import pandas as pd
from typing import Dict, List

from data.mimic_processor import MIMICIVProcessor, create_mimic_dataset_for_steganography
from data.mimic_dataset import MIMICDatasetBuilder, setup_mimic_steganography_project


def check_mimic_installation(mimic_path: str) -> Dict[str, bool]:
    """
    Check if MIMIC-IV dataset is properly installed.
    
    Args:
        mimic_path: Path to MIMIC-IV dataset
        
    Returns:
        Dictionary indicating which tables are available
    """
    mimic_path = Path(mimic_path)
    
    # Expected MIMIC-IV tables
    expected_tables = [
        'patients', 'admissions', 'diagnoses_icd', 'procedures_icd',
        'prescriptions', 'labevents', 'chartevents'
    ]
    
    # Possible subdirectories
    subdirs = ['', 'core', 'hosp', 'icu']
    
    table_status = {}
    
    for table in expected_tables:
        found = False
        for subdir in subdirs:
            search_dir = mimic_path / subdir if subdir else mimic_path
            
            # Check for different file formats
            possible_files = [
                search_dir / f"{table}.csv",
                search_dir / f"{table}.csv.gz",
                search_dir / f"{table}.parquet"
            ]
            
            for file_path in possible_files:
                if file_path.exists():
                    found = True
                    break
            
            if found:
                break
        
        table_status[table] = found
    
    return table_status


def analyze_mimic_dataset(mimic_path: str) -> Dict:
    """
    Analyze MIMIC-IV dataset and provide statistics.
    
    Args:
        mimic_path: Path to MIMIC-IV dataset
        
    Returns:
        Dictionary with dataset statistics
    """
    print("Analyzing MIMIC-IV dataset...")
    
    processor = MIMICIVProcessor(mimic_path, anonymize=True)
    
    try:
        # Load patients table for basic statistics
        patients_df = processor.load_mimic_table('patients')
        
        stats = {
            'total_patients': len(patients_df),
            'gender_distribution': patients_df['gender'].value_counts().to_dict(),
            'age_statistics': {
                'mean_age': patients_df['anchor_age'].mean(),
                'median_age': patients_df['anchor_age'].median(),
                'age_range': [patients_df['anchor_age'].min(), patients_df['anchor_age'].max()]
            }
        }
        
        # Try to get admission statistics
        try:
            admissions_df = processor.load_mimic_table('admissions')
            stats['total_admissions'] = len(admissions_df)
            stats['unique_patients_with_admissions'] = admissions_df['subject_id'].nunique()
            stats['admission_types'] = admissions_df['admission_type'].value_counts().to_dict()
        except Exception as e:
            print(f"Could not analyze admissions: {e}")
        
        # Try to get diagnosis statistics
        try:
            diagnoses_df = processor.load_mimic_table('diagnoses_icd')
            stats['total_diagnoses'] = len(diagnoses_df)
            stats['unique_icd_codes'] = diagnoses_df['icd_code'].nunique()
        except Exception as e:
            print(f"Could not analyze diagnoses: {e}")
        
        return stats
        
    except Exception as e:
        print(f"Error analyzing dataset: {e}")
        return {}


def create_sample_mimic_records(mimic_path: str, output_path: str, num_samples: int = 10):
    """
    Create sample MIMIC-IV records for demonstration.
    
    Args:
        mimic_path: Path to MIMIC-IV dataset
        output_path: Output path for sample records
        num_samples: Number of sample records to create
    """
    print(f"Creating {num_samples} sample MIMIC-IV records...")
    
    processor = MIMICIVProcessor(mimic_path, anonymize=True, max_record_size=2048)
    
    # Generate sample records
    sample_records = processor.get_random_patient_records(num_samples)
    
    # Save to file
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(sample_records, f, indent=2, default=str)
    
    print(f"Sample records saved to: {output_path}")
    
    # Print statistics
    if sample_records:
        record_sizes = [len(json.dumps(record).encode('utf-8')) for record in sample_records]
        print(f"Record statistics:")
        print(f"  Average size: {sum(record_sizes) / len(record_sizes):.1f} bytes")
        print(f"  Size range: {min(record_sizes)} - {max(record_sizes)} bytes")
        
        # Show sample record structure
        print(f"\nSample record structure:")
        sample_record = sample_records[0]
        for key, value in sample_record.items():
            if isinstance(value, list):
                print(f"  {key}: list with {len(value)} items")
            elif isinstance(value, dict):
                print(f"  {key}: dict with {len(value)} keys")
            else:
                print(f"  {key}: {type(value).__name__}")


def setup_mimic_for_steganography(
    mimic_path: str,
    image_dir: str,
    output_dir: str = "data/mimic_processed",
    num_records: int = 1000
):
    """
    Complete setup of MIMIC-IV for steganography research.
    
    Args:
        mimic_path: Path to MIMIC-IV dataset
        image_dir: Directory containing cover images
        output_dir: Output directory for processed data
        num_records: Number of medical records to generate
    """
    print("Setting up MIMIC-IV for steganography research...")
    
    # Check MIMIC-IV installation
    print("\n1. Checking MIMIC-IV installation...")
    table_status = check_mimic_installation(mimic_path)
    
    print("Table availability:")
    for table, available in table_status.items():
        status = "✓" if available else "✗"
        print(f"  {status} {table}")
    
    missing_tables = [table for table, available in table_status.items() if not available]
    if missing_tables:
        print(f"\nWarning: Missing tables: {missing_tables}")
        print("Some functionality may be limited.")
    
    # Analyze dataset
    print("\n2. Analyzing MIMIC-IV dataset...")
    stats = analyze_mimic_dataset(mimic_path)
    
    if stats:
        print("Dataset statistics:")
        print(f"  Total patients: {stats.get('total_patients', 'Unknown')}")
        print(f"  Total admissions: {stats.get('total_admissions', 'Unknown')}")
        print(f"  Mean age: {stats.get('age_statistics', {}).get('mean_age', 'Unknown'):.1f}")
        
        gender_dist = stats.get('gender_distribution', {})
        if gender_dist:
            print(f"  Gender distribution: {dict(gender_dist)}")
    
    # Create sample records
    print("\n3. Creating sample medical records...")
    sample_output = Path(output_dir) / "sample_records.json"
    create_sample_mimic_records(mimic_path, sample_output, 10)
    
    # Setup full steganography project
    print("\n4. Setting up steganography datasets...")
    dataset_paths = setup_mimic_steganography_project(
        mimic_path=mimic_path,
        image_dir=image_dir,
        output_dir=output_dir,
        num_records=num_records
    )
    
    # Create configuration file
    config = {
        'mimic_path': str(mimic_path),
        'image_dir': str(image_dir),
        'output_dir': str(output_dir),
        'dataset_paths': dataset_paths,
        'table_status': table_status,
        'dataset_stats': stats,
        'setup_timestamp': pd.Timestamp.now().isoformat()
    }
    
    config_path = Path(output_dir) / "mimic_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2, default=str)
    
    print(f"\n5. Setup complete!")
    print(f"Configuration saved to: {config_path}")
    print(f"Ready for steganography training with MIMIC-IV data!")
    
    return config


def validate_mimic_setup(config_path: str):
    """
    Validate MIMIC-IV setup for steganography.
    
    Args:
        config_path: Path to MIMIC configuration file
    """
    print("Validating MIMIC-IV setup...")
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check if all dataset files exist
        dataset_paths = config.get('dataset_paths', {})
        
        print("Dataset file validation:")
        for name, path in dataset_paths.items():
            exists = Path(path).exists()
            status = "✓" if exists else "✗"
            print(f"  {status} {name}: {path}")
        
        # Check image directory
        image_dir = config.get('image_dir', '')
        if Path(image_dir).exists():
            image_count = len(list(Path(image_dir).glob('*.jpg'))) + len(list(Path(image_dir).glob('*.png')))
            print(f"  ✓ Images: {image_count} files in {image_dir}")
        else:
            print(f"  ✗ Image directory not found: {image_dir}")
        
        print("Validation complete!")
        
    except Exception as e:
        print(f"Validation failed: {e}")


def main():
    parser = argparse.ArgumentParser(description='Setup MIMIC-IV for SteganoGAN')
    parser.add_argument('--mimic_path', type=str, required=True, 
                       help='Path to MIMIC-IV dataset')
    parser.add_argument('--image_dir', type=str, required=True,
                       help='Directory containing cover images')
    parser.add_argument('--output_dir', type=str, default='data/mimic_processed',
                       help='Output directory for processed data')
    parser.add_argument('--num_records', type=int, default=1000,
                       help='Number of medical records to generate')
    parser.add_argument('--action', type=str, default='setup',
                       choices=['setup', 'analyze', 'validate', 'sample'],
                       help='Action to perform')
    parser.add_argument('--config', type=str, default=None,
                       help='Configuration file path (for validate action)')
    
    args = parser.parse_args()
    
    if args.action == 'setup':
        setup_mimic_for_steganography(
            mimic_path=args.mimic_path,
            image_dir=args.image_dir,
            output_dir=args.output_dir,
            num_records=args.num_records
        )
    
    elif args.action == 'analyze':
        stats = analyze_mimic_dataset(args.mimic_path)
        print(json.dumps(stats, indent=2, default=str))
    
    elif args.action == 'validate':
        config_path = args.config or os.path.join(args.output_dir, 'mimic_config.json')
        validate_mimic_setup(config_path)
    
    elif args.action == 'sample':
        sample_output = os.path.join(args.output_dir, 'sample_records.json')
        create_sample_mimic_records(args.mimic_path, sample_output, 10)
    
    print("\nNext steps:")
    print("1. Verify the generated datasets")
    print("2. Run training with: python scripts/train.py --config mimic")
    print("3. Test with: python scripts/demo.py --medical_data <mimic_records.json>")


if __name__ == "__main__":
    main()

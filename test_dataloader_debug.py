#!/usr/bin/env python3
"""
Debug script for DataLoader issues.
"""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    print("Testing DataLoader...")
    
    import torch
    from torch.utils.data import DataLoader
    from src.data.dataset import SteganoDataset, create_dataloader
    
    # Test dataset creation
    dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_train_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,
        medical_data_ratio=1.0
    )
    print(f"✓ Dataset created, length: {len(dataset)}")
    
    # Test DataLoader creation
    dataloader = create_dataloader(
        dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        pin_memory=False
    )
    print("✓ DataLoader created")
    
    # Test getting a batch
    print("Testing batch retrieval...")
    for batch_idx, batch in enumerate(dataloader):
        print(f"✓ Batch {batch_idx} retrieved successfully")
        print(f"  Image batch shape: {batch['image'].shape}")
        print(f"  Medical data batch shape: {batch['medical_data'].shape}")
        print(f"  Batch size: {len(batch['image_path'])}")
        
        # Only test first batch
        if batch_idx == 0:
            break
    
    print("DataLoader test passed!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

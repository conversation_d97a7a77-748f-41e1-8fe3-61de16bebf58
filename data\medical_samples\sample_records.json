[{"patient_id": "P001", "timestamp": "2024-01-15T09:30:00Z", "symptoms": ["chest pain", "shortness of breath", "fatigue"], "vital_signs": {"temperature": 98.6, "blood_pressure": "140/90", "heart_rate": 88, "respiratory_rate": 18, "oxygen_saturation": 96}, "diagnosis": "hypertension with possible cardiac involvement", "lab_results": {"glucose": 125, "cholesterol": 220, "triglycerides": 180, "creatinine": 1.1, "bun": 18}, "medications": [{"name": "lisinopril", "dosage": "10mg", "frequency": "once daily"}, {"name": "aspirin", "dosage": "81mg", "frequency": "once daily"}], "treatment_plan": "lifestyle modifications, medication compliance, follow-up in 2 weeks", "notes": "Patient reports chest pain during exertion. ECG shows mild abnormalities. Recommend stress test.", "follow_up": "2024-01-29T09:30:00Z", "urgency": "moderate"}, {"patient_id": "P002", "timestamp": "2024-01-15T14:15:00Z", "symptoms": ["persistent cough", "fever", "body aches"], "vital_signs": {"temperature": 101.2, "blood_pressure": "118/75", "heart_rate": 92, "respiratory_rate": 20, "oxygen_saturation": 98}, "diagnosis": "viral upper respiratory infection", "lab_results": {"white_blood_cells": 8500, "neutrophils": 65, "lymphocytes": 30, "c_reactive_protein": 12}, "medications": [{"name": "acetaminophen", "dosage": "500mg", "frequency": "every 6 hours as needed"}, {"name": "dextromethorphan", "dosage": "15mg", "frequency": "every 4 hours as needed"}], "treatment_plan": "supportive care, rest, increased fluid intake", "notes": "Symptoms consistent with viral infection. No bacterial involvement suspected. Patient advised to return if symptoms worsen.", "follow_up": "2024-01-22T14:15:00Z", "urgency": "low"}, {"patient_id": "P003", "timestamp": "2024-01-16T11:00:00Z", "symptoms": ["severe headache", "nausea", "sensitivity to light"], "vital_signs": {"temperature": 98.4, "blood_pressure": "125/82", "heart_rate": 76, "respiratory_rate": 16, "oxygen_saturation": 99}, "diagnosis": "migraine headache", "lab_results": {"complete_blood_count": "normal", "electrolytes": "normal"}, "medications": [{"name": "sumatriptan", "dosage": "50mg", "frequency": "as needed for migraine"}, {"name": "metoclopramide", "dosage": "10mg", "frequency": "as needed for nausea"}], "treatment_plan": "acute migraine management, trigger identification, preventive measures", "notes": "Patient has history of migraines. Current episode severe. Responded well to treatment. Discussed trigger avoidance.", "follow_up": "2024-02-16T11:00:00Z", "urgency": "moderate"}, {"patient_id": "P004", "timestamp": "2024-01-16T16:45:00Z", "symptoms": ["joint pain", "morning stiffness", "swelling in hands"], "vital_signs": {"temperature": 98.8, "blood_pressure": "132/78", "heart_rate": 72, "respiratory_rate": 14, "oxygen_saturation": 99}, "diagnosis": "rheumatoid arthritis flare", "lab_results": {"rheumatoid_factor": "positive", "anti_ccp": "positive", "esr": 45, "crp": 18}, "medications": [{"name": "methotrexate", "dosage": "15mg", "frequency": "weekly"}, {"name": "prednisone", "dosage": "10mg", "frequency": "daily for 1 week"}, {"name": "folic_acid", "dosage": "5mg", "frequency": "weekly"}], "treatment_plan": "disease-modifying therapy adjustment, short-term steroid bridge, joint protection education", "notes": "Patient experiencing flare of known RA. Inflammatory markers elevated. Adjusting immunosuppressive therapy.", "follow_up": "2024-02-02T16:45:00Z", "urgency": "moderate"}, {"patient_id": "P005", "timestamp": "2024-01-17T08:30:00Z", "symptoms": ["frequent urination", "excessive thirst", "fatigue"], "vital_signs": {"temperature": 98.2, "blood_pressure": "138/85", "heart_rate": 80, "respiratory_rate": 16, "oxygen_saturation": 98}, "diagnosis": "type 2 diabetes mellitus, newly diagnosed", "lab_results": {"fasting_glucose": 185, "hba1c": 8.2, "random_glucose": 220, "ketones": "negative"}, "medications": [{"name": "metformin", "dosage": "500mg", "frequency": "twice daily with meals"}], "treatment_plan": "diabetes education, dietary counseling, glucose monitoring, lifestyle modifications", "notes": "New diagnosis of diabetes. Patient requires comprehensive education. Referred to diabetes educator and nutritionist.", "follow_up": "2024-01-31T08:30:00Z", "urgency": "high"}]
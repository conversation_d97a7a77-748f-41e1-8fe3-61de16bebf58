#!/usr/bin/env python3
"""
Generate synthetic MIMIC-IV-like data for development and testing.
This creates realistic medical records that match MIMIC-IV structure.
"""

import json
import random
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import argparse


class SyntheticMIMICGenerator:
    """Generate synthetic medical data that mimics MIMIC-IV structure."""

    def __init__(self, seed=42):
        random.seed(seed)
        np.random.seed(seed)

        # Medical vocabularies based on MIMIC-IV patterns
        self.icd10_codes = [
            'I50.9', 'J44.1', 'N18.6', 'E11.9', 'I25.10', 'F17.210',
            'Z87.891', 'I10', 'E78.5', 'K21.9', 'M79.3', 'R06.02',
            'R50.9', 'N39.0', 'K59.00', 'G93.1', 'R53.83', 'Z51.11'
        ]

        self.medications = [
            'Acetaminophen', 'Aspirin', 'Lisinopril', 'Metformin', 'Atorvastatin',
            'Omeprazole', 'Albuterol', 'Furosemide', 'Metoprolol', 'Insulin',
            'Warfarin', 'Prednisone', 'Amoxicillin', 'Morphine', 'Heparin'
        ]

        self.lab_items = {
            '50912': 'Creatinine',
            '50902': 'Chloride',
            '50931': 'Glucose',
            '50983': 'Sodium',
            '51006': 'Urea Nitrogen',
            '51221': 'Hematocrit',
            '51222': 'Hemoglobin',
            '51248': 'MCH',
            '51249': 'MCHC',
            '51250': 'MCV'
        }

        self.admission_types = ['EMERGENCY', 'ELECTIVE', 'URGENT', 'NEWBORN']
        self.admission_locations = [
            'EMERGENCY ROOM', 'PHYSICIAN REFERRAL', 'TRANSFER FROM HOSPITAL',
            'CLINIC REFERRAL', 'TRANSFER FROM SKILLED NURSING FACILITY'
        ]

        self.discharge_locations = [
            'HOME', 'SKILLED NURSING FACILITY', 'HOME HEALTH CARE',
            'REHAB', 'HOSPICE', 'LONG TERM CARE HOSPITAL'
        ]

        self.ethnicities = [
            'WHITE', 'BLACK/AFRICAN AMERICAN', 'HISPANIC/LATINO',
            'ASIAN', 'OTHER', 'UNKNOWN'
        ]

        self.insurances = ['Medicare', 'Medicaid', 'Private', 'Self Pay']

    def generate_patient_demographics(self, patient_id: int) -> dict:
        """Generate patient demographic data."""
        return {
            'subject_id': patient_id,
            'gender': random.choice(['M', 'F']),
            'anchor_age': random.randint(18, 89),
            'anchor_year': random.randint(2008, 2019),
            'dod': None if random.random() > 0.1 else '2019-01-01'  # 10% mortality
        }

    def generate_admission(self, patient_id: int, hadm_id: int) -> dict:
        """Generate hospital admission data."""
        admit_time = datetime(2019, 1, 1) + timedelta(days=random.randint(0, 365))
        los = random.randint(1, 30)  # Length of stay
        discharge_time = admit_time + timedelta(days=los)

        return {
            'subject_id': patient_id,
            'hadm_id': hadm_id,
            'admittime': admit_time.isoformat(),
            'dischtime': discharge_time.isoformat(),
            'deathtime': None if random.random() > 0.05 else discharge_time.isoformat(),
            'admission_type': random.choice(self.admission_types),
            'admission_location': random.choice(self.admission_locations),
            'discharge_location': random.choice(self.discharge_locations),
            'insurance': random.choice(self.insurances),
            'language': 'ENGLISH',
            'marital_status': random.choice(['MARRIED', 'SINGLE', 'DIVORCED', 'WIDOWED']),
            'ethnicity': random.choice(self.ethnicities),
            'edregtime': (admit_time - timedelta(hours=random.randint(1, 6))).isoformat(),
            'edouttime': admit_time.isoformat(),
            'hospital_expire_flag': 1 if random.random() < 0.05 else 0
        }

    def generate_diagnoses(self, patient_id: int, hadm_id: int) -> list:
        """Generate diagnosis data."""
        num_diagnoses = random.randint(1, 8)
        diagnoses = []

        for seq_num in range(1, num_diagnoses + 1):
            diagnoses.append({
                'subject_id': patient_id,
                'hadm_id': hadm_id,
                'seq_num': seq_num,
                'icd_code': random.choice(self.icd10_codes),
                'icd_version': 10
            })

        return diagnoses

    def generate_prescriptions(self, patient_id: int, hadm_id: int) -> list:
        """Generate prescription data."""
        num_prescriptions = random.randint(0, 10)
        prescriptions = []

        for _ in range(num_prescriptions):
            drug = random.choice(self.medications)
            prescriptions.append({
                'subject_id': patient_id,
                'hadm_id': hadm_id,
                'pharmacy_id': random.randint(1000, 9999),
                'starttime': datetime(2019, 1, 1).isoformat(),
                'stoptime': (datetime(2019, 1, 1) + timedelta(days=random.randint(1, 10))).isoformat(),
                'drug_type': 'MAIN',
                'drug': drug,
                'drug_name_poe': drug,
                'drug_name_generic': drug.lower(),
                'formulary_drug_cd': f"{drug[:3].upper()}{random.randint(100, 999)}",
                'gsn': str(random.randint(100000, 999999)),
                'ndc': f"{random.randint(10000, 99999)}-{random.randint(100, 999)}-{random.randint(10, 99)}",
                'prod_strength': f"{random.randint(1, 100)}mg",
                'dose_val_rx': str(random.randint(1, 100)),
                'dose_unit_rx': random.choice(['mg', 'mcg', 'g', 'mL']),
                'form_val_disp': str(random.randint(1, 30)),
                'form_unit_disp': random.choice(['TAB', 'CAP', 'mL', 'SUPP']),
                'doses_per_24_hrs': random.randint(1, 4),
                'route': random.choice(['PO', 'IV', 'IM', 'SC', 'TOP'])
            })

        return prescriptions

    def generate_lab_events(self, patient_id: int, hadm_id: int) -> list:
        """Generate laboratory event data."""
        num_labs = random.randint(5, 25)
        lab_events = []

        for _ in range(num_labs):
            itemid = random.choice(list(self.lab_items.keys()))

            # Generate realistic values based on lab type
            if itemid == '50912':  # Creatinine
                value = round(random.uniform(0.5, 3.0), 2)
                valueuom = 'mg/dL'
                flag = 'abnormal' if value > 1.2 else None
            elif itemid == '50931':  # Glucose
                value = round(random.uniform(70, 300), 0)
                valueuom = 'mg/dL'
                flag = 'abnormal' if value > 140 or value < 70 else None
            elif itemid == '51222':  # Hemoglobin
                value = round(random.uniform(8.0, 16.0), 1)
                valueuom = 'g/dL'
                flag = 'abnormal' if value < 12.0 else None
            else:
                value = round(random.uniform(10, 100), 1)
                valueuom = random.choice(['mg/dL', 'mmol/L', 'g/dL', '%'])
                flag = 'abnormal' if random.random() < 0.3 else None

            lab_events.append({
                'subject_id': patient_id,
                'hadm_id': hadm_id,
                'itemid': itemid,
                'charttime': (datetime(2019, 1, 1) + timedelta(hours=random.randint(0, 168))).isoformat(),
                'storetime': (datetime(2019, 1, 1) + timedelta(hours=random.randint(0, 168))).isoformat(),
                'value': str(value),
                'valuenum': value,
                'valueuom': valueuom,
                'ref_range_lower': None,
                'ref_range_upper': None,
                'flag': flag,
                'priority': random.choice(['ROUTINE', 'STAT', 'URGENT']),
                'comments': None
            })

        return lab_events

    def generate_complete_record(self, patient_id: int) -> dict:
        """Generate a complete MIMIC-IV-like medical record."""
        hadm_id = patient_id * 100 + random.randint(1, 99)

        # Generate all components
        demographics = self.generate_patient_demographics(patient_id)
        admission = self.generate_admission(patient_id, hadm_id)
        diagnoses = self.generate_diagnoses(patient_id, hadm_id)
        prescriptions = self.generate_prescriptions(patient_id, hadm_id)
        lab_events = self.generate_lab_events(patient_id, hadm_id)

        # Create comprehensive record
        record = {
            'record_type': 'synthetic_mimic_iv',
            'patient_id': f"SYNTH_{patient_id:06d}",
            'timestamp': datetime.now().isoformat(),
            'source': 'Synthetic MIMIC-IV Generator',
            'version': '1.0',

            # Demographics
            'gender': demographics['gender'],
            'anchor_age': demographics['anchor_age'],

            # Admission details
            'admission': {
                'admission_id': str(hadm_id),
                'admission_type': admission['admission_type'],
                'admission_location': admission['admission_location'],
                'discharge_location': admission['discharge_location'],
                'insurance': admission['insurance'],
                'ethnicity': admission['ethnicity'],
                'marital_status': admission['marital_status'],
                'hospital_expire_flag': admission['hospital_expire_flag']
            },

            # Medical data
            'diagnoses': diagnoses[:5],  # Limit for size
            'prescriptions': prescriptions[:8],  # Limit for size
            'lab_results': lab_events[:15],  # Limit for size

            # Metadata
            'record_size': 0,  # Will be calculated
            'anonymized': True,
            'synthetic': True
        }

        # Calculate record size
        record['record_size'] = len(json.dumps(record))

        return record


def create_synthetic_mimic_dataset(
    output_dir: str,
    num_patients: int = 1000,
    train_ratio: float = 0.7,
    val_ratio: float = 0.15
) -> dict:
    """Create a complete synthetic MIMIC-IV dataset."""

    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"Generating {num_patients} synthetic MIMIC-IV records...")

    generator = SyntheticMIMICGenerator()
    all_records = []

    # Generate records
    for patient_id in range(1, num_patients + 1):
        if patient_id % 100 == 0:
            print(f"Generated {patient_id}/{num_patients} records...")

        record = generator.generate_complete_record(patient_id)
        all_records.append(record)

    # Split into train/val/test
    random.shuffle(all_records)

    train_size = int(len(all_records) * train_ratio)
    val_size = int(len(all_records) * val_ratio)

    train_records = all_records[:train_size]
    val_records = all_records[train_size:train_size + val_size]
    test_records = all_records[train_size + val_size:]

    # Save datasets
    datasets = {
        'train': train_records,
        'val': val_records,
        'test': test_records,
        'sample': all_records[:10]  # Sample for quick testing
    }

    file_paths = {}
    for split_name, records in datasets.items():
        file_path = output_dir / f"synthetic_{split_name}_records.json"
        with open(file_path, 'w') as f:
            json.dump(records, f, indent=2, default=str)

        file_paths[split_name] = str(file_path)
        print(f"Saved {len(records)} {split_name} records to {file_path}")

    # Create configuration
    config = {
        'dataset_type': 'synthetic_mimic_iv',
        'num_patients': num_patients,
        'splits': {k: len(v) for k, v in datasets.items()},
        'file_paths': file_paths,
        'generation_timestamp': datetime.now().isoformat(),
        'generator_version': '1.0'
    }

    config_path = output_dir / "synthetic_mimic_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2, default=str)

    print(f"\nSynthetic MIMIC-IV dataset created successfully!")
    print(f"Configuration saved to: {config_path}")

    return config


def create_csv_exports(output_dir: str):
    """Create CSV exports of the synthetic data for easier analysis."""
    output_dir = Path(output_dir)

    print("Creating CSV exports...")

    # Load JSON data
    with open(output_dir / "synthetic_train_records.json", 'r') as f:
        train_records = json.load(f)

    # Create flattened CSV for patient demographics and admissions
    patient_data = []
    diagnosis_data = []
    prescription_data = []
    lab_data = []

    for record in train_records:
        # Patient demographics and admission
        patient_row = {
            'patient_id': record['patient_id'],
            'gender': record['gender'],
            'anchor_age': record['anchor_age'],
            'admission_id': record['admission']['admission_id'],
            'admission_type': record['admission']['admission_type'],
            'admission_location': record['admission']['admission_location'],
            'discharge_location': record['admission']['discharge_location'],
            'insurance': record['admission']['insurance'],
            'ethnicity': record['admission']['ethnicity'],
            'marital_status': record['admission']['marital_status'],
            'hospital_expire_flag': record['admission']['hospital_expire_flag'],
            'record_size': record['record_size']
        }
        patient_data.append(patient_row)

        # Diagnoses
        for diag in record['diagnoses']:
            diag_row = {
                'patient_id': record['patient_id'],
                'admission_id': record['admission']['admission_id'],
                'seq_num': diag['seq_num'],
                'icd_code': diag['icd_code'],
                'icd_version': diag['icd_version']
            }
            diagnosis_data.append(diag_row)

        # Prescriptions
        for presc in record['prescriptions']:
            presc_row = {
                'patient_id': record['patient_id'],
                'admission_id': record['admission']['admission_id'],
                'drug': presc['drug'],
                'dose_val_rx': presc['dose_val_rx'],
                'dose_unit_rx': presc['dose_unit_rx'],
                'route': presc['route'],
                'doses_per_24_hrs': presc['doses_per_24_hrs']
            }
            prescription_data.append(presc_row)

        # Lab results
        for lab in record['lab_results']:
            lab_row = {
                'patient_id': record['patient_id'],
                'admission_id': record['admission']['admission_id'],
                'itemid': lab['itemid'],
                'value': lab['value'],
                'valuenum': lab['valuenum'],
                'valueuom': lab['valueuom'],
                'flag': lab['flag'],
                'priority': lab['priority']
            }
            lab_data.append(lab_row)

    # Save CSV files
    pd.DataFrame(patient_data).to_csv(output_dir / "synthetic_patients.csv", index=False)
    pd.DataFrame(diagnosis_data).to_csv(output_dir / "synthetic_diagnoses.csv", index=False)
    pd.DataFrame(prescription_data).to_csv(output_dir / "synthetic_prescriptions.csv", index=False)
    pd.DataFrame(lab_data).to_csv(output_dir / "synthetic_lab_events.csv", index=False)

    print(f"CSV files created:")
    print(f"  - synthetic_patients.csv ({len(patient_data)} patients)")
    print(f"  - synthetic_diagnoses.csv ({len(diagnosis_data)} diagnoses)")
    print(f"  - synthetic_prescriptions.csv ({len(prescription_data)} prescriptions)")
    print(f"  - synthetic_lab_events.csv ({len(lab_data)} lab events)")


def generate_data_summary(output_dir: str):
    """Generate a comprehensive summary of the synthetic dataset."""
    output_dir = Path(output_dir)

    # Load configuration
    with open(output_dir / "synthetic_mimic_config.json", 'r') as f:
        config = json.load(f)

    # Load sample data for analysis
    with open(output_dir / "synthetic_train_records.json", 'r') as f:
        train_records = json.load(f)

    summary = {
        "dataset_overview": {
            "total_patients": config['num_patients'],
            "train_patients": config['splits']['train'],
            "val_patients": config['splits']['val'],
            "test_patients": config['splits']['test'],
            "generation_date": config['generation_timestamp']
        },
        "data_characteristics": {
            "avg_diagnoses_per_patient": np.mean([len(r['diagnoses']) for r in train_records]),
            "avg_prescriptions_per_patient": np.mean([len(r['prescriptions']) for r in train_records]),
            "avg_lab_results_per_patient": np.mean([len(r['lab_results']) for r in train_records]),
            "avg_record_size_bytes": np.mean([r['record_size'] for r in train_records])
        },
        "demographics": {
            "gender_distribution": {
                "male": sum(1 for r in train_records if r['gender'] == 'M'),
                "female": sum(1 for r in train_records if r['gender'] == 'F')
            },
            "age_statistics": {
                "mean_age": np.mean([r['anchor_age'] for r in train_records]),
                "min_age": min([r['anchor_age'] for r in train_records]),
                "max_age": max([r['anchor_age'] for r in train_records])
            }
        },
        "medical_data": {
            "unique_icd_codes": len(set([d['icd_code'] for r in train_records for d in r['diagnoses']])),
            "unique_medications": len(set([p['drug'] for r in train_records for p in r['prescriptions']])),
            "unique_lab_items": len(set([l['itemid'] for r in train_records for l in r['lab_results']]))
        },
        "steganography_compatibility": {
            "records_under_512_bytes": sum(1 for r in train_records if r['record_size'] <= 512),
            "records_under_1024_bytes": sum(1 for r in train_records if r['record_size'] <= 1024),
            "records_under_2048_bytes": sum(1 for r in train_records if r['record_size'] <= 2048),
            "max_record_size": max([r['record_size'] for r in train_records]),
            "min_record_size": min([r['record_size'] for r in train_records])
        }
    }

    # Save summary
    summary_path = output_dir / "synthetic_data_summary.json"
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2, default=str)

    print(f"\nDataset Summary:")
    print(f"================")
    print(f"Total Patients: {summary['dataset_overview']['total_patients']}")
    print(f"Average Diagnoses per Patient: {summary['data_characteristics']['avg_diagnoses_per_patient']:.1f}")
    print(f"Average Prescriptions per Patient: {summary['data_characteristics']['avg_prescriptions_per_patient']:.1f}")
    print(f"Average Lab Results per Patient: {summary['data_characteristics']['avg_lab_results_per_patient']:.1f}")
    print(f"Average Record Size: {summary['data_characteristics']['avg_record_size_bytes']:.0f} bytes")
    print(f"Records ≤ 1024 bytes: {summary['steganography_compatibility']['records_under_1024_bytes']}/{len(train_records)} ({100*summary['steganography_compatibility']['records_under_1024_bytes']/len(train_records):.1f}%)")
    print(f"Summary saved to: {summary_path}")

    return summary


def main():
    parser = argparse.ArgumentParser(description='Generate synthetic MIMIC-IV data')
    parser.add_argument('--output_dir', type=str, default='data/medical_samples',
                       help='Output directory for synthetic data')
    parser.add_argument('--num_patients', type=int, default=1000,
                       help='Number of patients to generate')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')
    parser.add_argument('--create_csv', action='store_true',
                       help='Create CSV exports of the data')
    parser.add_argument('--summary_only', action='store_true',
                       help='Only generate summary (skip data generation)')

    args = parser.parse_args()

    # Set random seed
    random.seed(args.seed)
    np.random.seed(args.seed)

    if args.summary_only:
        # Only generate summary and CSV exports
        if args.create_csv:
            create_csv_exports(args.output_dir)
        generate_data_summary(args.output_dir)
    else:
        # Generate dataset
        config = create_synthetic_mimic_dataset(
            output_dir=args.output_dir,
            num_patients=args.num_patients
        )

        # Create CSV exports if requested
        if args.create_csv:
            create_csv_exports(args.output_dir)

        # Generate summary
        generate_data_summary(args.output_dir)

        print(f"\nNext steps:")
        print(f"1. Test with synthetic data:")
        print(f"   python scripts/demo.py --medical_data {config['file_paths']['sample']}")
        print(f"2. Train with synthetic data:")
        print(f"   python scripts/train.py --config mimic_fast")
        print(f"3. Replace with real MIMIC-IV data once available")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Quick analysis and integration of MIMIC-IV demo data.
Direct approach to create unified dataset with real medical data.
"""

import pandas as pd
import json
import numpy as np
import random
from pathlib import Path
from datetime import datetime


def quick_mimic_analysis():
    """Quick analysis of MIMIC-IV demo data structure."""
    
    print("🔍 Quick MIMIC-IV Demo Analysis")
    print("=" * 50)
    
    mimic_path = Path("mimic-iv-clinical-database-demo-2.2")
    
    # Analyze key tables
    tables_info = {}
    
    # Patients table
    try:
        patients_df = pd.read_csv(mimic_path / "hosp/patients.csv", nrows=10)
        tables_info["patients"] = {
            "columns": list(patients_df.columns),
            "sample_count": len(patients_df),
            "sample_data": patients_df.head(3).to_dict('records')
        }
        print(f"✅ Patients: {len(patients_df)} rows, columns: {list(patients_df.columns)}")
    except Exception as e:
        print(f"❌ Patients table error: {e}")
    
    # Diagnoses table
    try:
        diagnoses_df = pd.read_csv(mimic_path / "hosp/diagnoses_icd.csv", nrows=20)
        tables_info["diagnoses"] = {
            "columns": list(diagnoses_df.columns),
            "sample_count": len(diagnoses_df),
            "sample_data": diagnoses_df.head(3).to_dict('records')
        }
        print(f"✅ Diagnoses: {len(diagnoses_df)} rows, columns: {list(diagnoses_df.columns)}")
    except Exception as e:
        print(f"❌ Diagnoses table error: {e}")
    
    # Prescriptions table
    try:
        prescriptions_df = pd.read_csv(mimic_path / "hosp/prescriptions.csv", nrows=20)
        tables_info["prescriptions"] = {
            "columns": list(prescriptions_df.columns),
            "sample_count": len(prescriptions_df),
            "sample_data": prescriptions_df.head(3).to_dict('records')
        }
        print(f"✅ Prescriptions: {len(prescriptions_df)} rows, columns: {list(prescriptions_df.columns)}")
    except Exception as e:
        print(f"❌ Prescriptions table error: {e}")
    
    # Lab events table
    try:
        labevents_df = pd.read_csv(mimic_path / "hosp/labevents.csv", nrows=30)
        tables_info["labevents"] = {
            "columns": list(labevents_df.columns),
            "sample_count": len(labevents_df),
            "sample_data": labevents_df.head(3).to_dict('records')
        }
        print(f"✅ Lab Events: {len(labevents_df)} rows, columns: {list(labevents_df.columns)}")
    except Exception as e:
        print(f"❌ Lab Events table error: {e}")
    
    # Save analysis
    analysis_dir = Path("analysis_results")
    analysis_dir.mkdir(exist_ok=True)
    
    with open(analysis_dir / "quick_mimic_analysis.json", 'w') as f:
        json.dump(tables_info, f, indent=2, default=str)
    
    print(f"📄 Analysis saved to: analysis_results/quick_mimic_analysis.json")
    return tables_info


def create_sample_mimic_records(num_records=20):
    """Create sample unified records from MIMIC-IV data."""
    
    print(f"\n🏥 Creating {num_records} Sample MIMIC-IV Records")
    print("=" * 50)
    
    mimic_path = Path("mimic-iv-clinical-database-demo-2.2")
    
    try:
        # Load core tables with limited rows
        patients_df = pd.read_csv(mimic_path / "hosp/patients.csv", nrows=num_records)
        admissions_df = pd.read_csv(mimic_path / "hosp/admissions.csv", nrows=num_records*2)
        diagnoses_df = pd.read_csv(mimic_path / "hosp/diagnoses_icd.csv", nrows=num_records*3)
        prescriptions_df = pd.read_csv(mimic_path / "hosp/prescriptions.csv", nrows=num_records*2)
        labevents_df = pd.read_csv(mimic_path / "hosp/labevents.csv", nrows=num_records*5)
        
        print(f"📊 Loaded tables successfully")
        
        unified_records = []
        
        for _, patient in patients_df.iterrows():
            subject_id = patient['subject_id']
            
            # Create base record
            record = {
                "record_id": f"MIMIC_{subject_id}",
                "source": "mimic_iv_demo",
                "timestamp": datetime.now().isoformat()[:19],
                "patient": {
                    "age": int(patient.get('anchor_age', 0)),
                    "gender": patient.get('gender', 'U')
                },
                "medical_data": {
                    "diagnoses": [],
                    "prescriptions": [],
                    "lab_results": []
                }
            }
            
            # Get patient's admission
            patient_admissions = admissions_df[admissions_df['subject_id'] == subject_id]
            if not patient_admissions.empty:
                hadm_id = patient_admissions.iloc[0]['hadm_id']
                
                # Add diagnoses
                patient_diagnoses = diagnoses_df[diagnoses_df['hadm_id'] == hadm_id].head(2)
                for _, diagnosis in patient_diagnoses.iterrows():
                    record["medical_data"]["diagnoses"].append({
                        "icd_code": str(diagnosis.get('icd_code', '')),
                        "seq_num": int(diagnosis.get('seq_num', 1))
                    })
                
                # Add prescriptions
                patient_prescriptions = prescriptions_df[prescriptions_df['hadm_id'] == hadm_id].head(2)
                for _, prescription in patient_prescriptions.iterrows():
                    drug_name = str(prescription.get('drug', 'Unknown'))[:50]  # Limit length
                    record["medical_data"]["prescriptions"].append({
                        "drug": drug_name,
                        "dose": "Standard",
                        "frequency": "As needed"
                    })
                
                # Add lab results
                patient_labs = labevents_df[labevents_df['hadm_id'] == hadm_id].head(3)
                for _, lab in patient_labs.iterrows():
                    record["medical_data"]["lab_results"].append({
                        "item_id": str(lab.get('itemid', '')),
                        "value": lab.get('valuenum', ''),
                        "unit": str(lab.get('valueuom', ''))[:10],  # Limit length
                        "flag": None
                    })
            
            # Calculate record size
            record_json = json.dumps(record, separators=(',', ':'))
            record["record_size"] = len(record_json.encode('utf-8'))
            
            # Only include if size is reasonable
            if record["record_size"] <= 1024:
                unified_records.append(record)
            
            if len(unified_records) >= num_records:
                break
        
        print(f"✅ Created {len(unified_records)} unified MIMIC-IV records")
        
        # Save sample records
        output_dir = Path("data/unified_medical_real")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(output_dir / "mimic_sample_records.json", 'w') as f:
            json.dump(unified_records, f, indent=1, separators=(',', ':'))
        
        print(f"💾 Saved to: data/unified_medical_real/mimic_sample_records.json")
        
        return unified_records
        
    except Exception as e:
        print(f"❌ Error creating MIMIC records: {e}")
        return []


def combine_with_synthetic_data():
    """Combine MIMIC-IV sample with synthetic data."""
    
    print(f"\n🔄 Combining MIMIC-IV with Synthetic Data")
    print("=" * 50)
    
    # Load MIMIC-IV sample
    mimic_path = Path("data/unified_medical_real/mimic_sample_records.json")
    if mimic_path.exists():
        with open(mimic_path, 'r') as f:
            mimic_records = json.load(f)
        print(f"📊 Loaded {len(mimic_records)} MIMIC-IV records")
    else:
        print("❌ MIMIC-IV sample not found")
        return
    
    # Load synthetic data
    synthetic_path = Path("data/medical_samples/compact_train_records.json")
    if synthetic_path.exists():
        with open(synthetic_path, 'r') as f:
            synthetic_records = json.load(f)
        
        # Take a subset to balance with MIMIC data
        synthetic_subset = random.sample(synthetic_records, min(len(synthetic_records), len(mimic_records) * 4))
        print(f"📊 Using {len(synthetic_subset)} synthetic records")
    else:
        print("❌ Synthetic data not found")
        return
    
    # Combine datasets
    all_records = mimic_records + synthetic_subset
    random.shuffle(all_records)
    
    # Create splits
    train_size = int(0.7 * len(all_records))
    val_size = int(0.15 * len(all_records))
    
    train_records = all_records[:train_size]
    val_records = all_records[train_size:train_size + val_size]
    test_records = all_records[train_size + val_size:]
    
    # Save datasets
    output_dir = Path("data/unified_medical_real")
    datasets = {
        'train': train_records,
        'val': val_records,
        'test': test_records,
        'sample': all_records[:20]
    }
    
    file_paths = {}
    for split_name, records in datasets.items():
        file_path = output_dir / f"unified_real_{split_name}_records.json"
        with open(file_path, 'w') as f:
            json.dump(records, f, indent=1, separators=(',', ':'))
        
        file_paths[split_name] = str(file_path)
        print(f"💾 Saved {len(records)} {split_name} records")
    
    # Create configuration
    mimic_count = len(mimic_records)
    synthetic_count = len(synthetic_subset)
    
    config = {
        "creation_timestamp": datetime.now().isoformat(),
        "dataset_type": "unified_medical_real",
        "total_records": len(all_records),
        "data_sources": {
            "mimic_iv_demo": {
                "count": mimic_count,
                "percentage": mimic_count / len(all_records) * 100
            },
            "synthetic": {
                "count": synthetic_count,
                "percentage": synthetic_count / len(all_records) * 100
            }
        },
        "file_paths": file_paths,
        "statistics": {
            "avg_record_size": np.mean([r.get("record_size", 0) for r in all_records]),
            "records_under_1024": sum(1 for r in all_records if r.get("record_size", 0) <= 1024)
        }
    }
    
    config_path = output_dir / "unified_real_dataset_config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2, default=str)
    
    print(f"\n📊 Unified Dataset Created:")
    print(f"   Total Records: {len(all_records)}")
    print(f"   MIMIC-IV: {mimic_count} ({mimic_count/len(all_records)*100:.1f}%)")
    print(f"   Synthetic: {synthetic_count} ({synthetic_count/len(all_records)*100:.1f}%)")
    print(f"   Configuration: {config_path}")
    
    return file_paths


def main():
    print("🏥 Quick MIMIC-IV Integration")
    print("=" * 60)
    
    # Step 1: Analyze MIMIC-IV structure
    tables_info = quick_mimic_analysis()
    
    # Step 2: Create sample MIMIC-IV records
    mimic_records = create_sample_mimic_records(20)
    
    # Step 3: Combine with synthetic data
    if mimic_records:
        file_paths = combine_with_synthetic_data()
        
        if file_paths:
            print(f"\n🚀 Next Steps:")
            print(f"1. Test unified dataset:")
            print(f"   python scripts/demo.py --medical_data {file_paths['sample']}")
            print(f"2. Validate integration:")
            print(f"   python scripts/test_complete_system.py --synthetic_data {file_paths['sample']}")
            print(f"3. Begin training with real+synthetic data")
    
    print(f"\n✅ MIMIC-IV Integration Complete!")


if __name__ == "__main__":
    main()

# Secure Medical Data Transmission using ECC and SteganoGAN

## PhD Project Overview

This PhD research project implements a novel approach to secure medical data transmission by combining **Elliptic Curve Cryptography (ECC)** with **SteganoGAN** (Steganographic Generative Adversarial Network) for telemedicine applications. The system encrypts sensitive medical information using ECC and then hides the encrypted data within realistic medical images using advanced steganographic techniques.

### Research Objectives

1. **Security Enhancement**: Implement ECC encryption for medical data before steganographic embedding
2. **Imperceptible Embedding**: Use SteganoGAN to hide encrypted medical data in realistic images
3. **Medical Context Preservation**: Maintain diagnostic quality of medical images while embedding data
4. **Telemedicine Integration**: Develop a practical system for secure patient-doctor communication
5. **Performance Evaluation**: Comprehensive analysis of security, quality, and robustness metrics

## Project Structure

```
├── README.md                 # Project documentation
├── requirements.txt          # Python dependencies
├── config/                   # Configuration files
│   ├── model_config.py      # Model hyperparameters
│   └── training_config.py   # Training configurations
├── src/                     # Source code
│   ├── models/              # Neural network models
│   │   ├── generator.py     # SteganoGAN Generator
│   │   ├── discriminator.py # Discriminator network
│   │   └── decoder.py       # Data extraction decoder
│   ├── data/                # Data handling utilities
│   │   ├── dataset.py       # Dataset classes
│   │   └── preprocessing.py # Data preprocessing
│   ├── training/            # Training pipeline
│   │   ├── trainer.py       # Main training loop
│   │   └── losses.py        # Loss functions
│   ├── evaluation/          # Evaluation metrics
│   │   ├── metrics.py       # Quality and steganographic metrics
│   │   └── visualization.py # Result visualization
│   └── utils/               # Utility functions
│       ├── helpers.py       # General utilities
│       └── medical_utils.py # Medical data specific utilities
├── data/                    # Data directory
│   ├── raw/                 # Raw datasets
│   ├── processed/           # Processed datasets
│   └── medical_samples/     # Sample medical data
├── models/                  # Saved model checkpoints
├── results/                 # Training results and outputs
│   ├── images/              # Generated images
│   ├── logs/                # Training logs
│   └── metrics/             # Evaluation results
├── notebooks/               # Jupyter notebooks for experimentation
├── tests/                   # Unit tests
└── scripts/                 # Training and evaluation scripts
    ├── train.py             # Main training script
    ├── evaluate.py          # Evaluation script
    └── demo.py              # Demonstration script
```

## Key Features

### SteganoGAN Architecture
- **Generator**: Creates realistic medical images with embedded hidden data
- **Discriminator**: Ensures generated images are indistinguishable from real images
- **Decoder**: Extracts hidden medical data from steganographic images

### Medical Data Support
- **MIMIC-IV Integration**: Full support for MIMIC-IV dataset
- **Real Medical Records**: Authentic patient data, diagnoses, lab results, prescriptions
- **HIPAA Compliance**: Built-in anonymization and privacy protection
- **Multiple Formats**: JSON, CSV, and structured medical data support

### MIMIC-IV Dataset Integration
- **Authentic Medical Data**: Real patient records from MIMIC-IV database
- **Comprehensive Records**: Demographics, diagnoses, lab results, prescriptions
- **Automatic Anonymization**: Patient privacy protection with hash-based IDs
- **Scalable Processing**: Handle thousands of medical records efficiently

### Evaluation Metrics
- **Image Quality**: PSNR, SSIM, FID scores
- **Steganographic Performance**: Embedding capacity, extraction accuracy
- **Security**: Resistance to steganalysis attacks

## Technical Specifications

### Model Architecture
- Generator: U-Net based architecture with attention mechanisms
- Discriminator: PatchGAN discriminator for local realism
- Decoder: Lightweight CNN for data extraction

### Training Strategy
- Adversarial training with balanced loss functions
- Progressive training for stable convergence
- Data augmentation for robustness

### Security Features
- Imperceptible embedding (< 0.5 dB PSNR degradation)
- Robust against common image processing operations
- Secure key-based data extraction

## Getting Started

1. **Environment Setup**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Data Preparation**:
   - Place training images in `data/raw/`
   - Prepare medical data samples in `data/medical_samples/`

3. **Training**:
   ```bash
   python scripts/train.py --config config/training_config.py
   ```

4. **MIMIC-IV Setup** (Optional):
   ```bash
   # Setup MIMIC-IV dataset integration
   python scripts/setup_mimic.py \
       --mimic_path /path/to/mimic-iv \
       --image_dir data/raw \
       --num_records 1000
   ```

5. **Training with MIMIC-IV**:
   ```bash
   # Train with real medical data
   python scripts/train.py --config mimic
   ```

6. **Evaluation**:
   ```bash
   python scripts/evaluate.py --model models/steganogan_best.pth
   ```

## Research Contributions

1. **Novel Architecture**: Specialized SteganoGAN for medical data
2. **Medical Data Security**: HIPAA-compliant steganographic transmission
3. **Robustness**: Resistant to common image processing attacks
4. **Quality Preservation**: Maintains diagnostic image quality

## Future Work

- Integration with Edwards curve ECC encryption
- Real-time telemedicine platform integration
- Advanced steganalysis resistance
- Multi-modal medical data support

## License

This project is developed for academic research purposes.

## Contact

[Your contact information for the PhD project]

# SteganoGAN Training Guide

This guide provides comprehensive instructions for training the SteganoGAN model with your unified medical dataset.

## Quick Start

### 1. Start Training Immediately
```bash
# Fast training (recommended for initial testing)
python scripts/start_training.py --config unified_real_fast

# Full training (for production models)
python scripts/start_training.py --config unified_real
```

### 2. Monitor Training Progress
```bash
# Open TensorBoard to monitor training
tensorboard --logdir results/logs/unified_real_fast
```

## Training Configurations

### Available Configurations

| Configuration | Epochs | Batch Size | Time Estimate | Use Case |
|---------------|--------|------------|---------------|----------|
| `unified_real_fast` | 100 | 20 | 4-8 hours | Development & Testing |
| `unified_real` | 250 | 16 | 12-24 hours | Production Training |

### Configuration Details

#### `unified_real_fast` - Quick Training
- **Purpose**: Fast iteration and testing
- **Features**: No progressive training, higher batch size
- **Best for**: Initial experiments, debugging, proof of concept

#### `unified_real` - Full Training  
- **Purpose**: Production-quality model training
- **Features**: Progressive resolution training (128→192→256)
- **Best for**: Final models, research results, deployment

## Dataset Information

### Current Dataset Composition
- **Training Records**: ~80 records (mixed MIMIC-IV + synthetic)
- **Validation Records**: ~20 records
- **Medical Images**: 100 images across 4 modalities
  - Chest X-ray: 25 images (256x256)
  - CT Scan: 25 images (256x256)
  - MRI: 25 images (256x256)
  - Ultrasound: 25 images (256x256)

### Data Format Support
The training system automatically handles:
- **MIMIC-IV Format**: Real clinical records from MIMIC-IV demo
- **Compact Medical Format**: Synthetic medical records
- **Mixed Datasets**: Seamless integration of both formats

## Training Process

### 1. Preprocessing
- Medical records are automatically normalized to consistent format
- Images are resized to 256x256 and normalized to [-1, 1]
- Data augmentation applied during training (rotation, flip, color jitter)

### 2. Model Architecture
- **Generator**: Creates steganographic images with embedded medical data
- **Discriminator**: Distinguishes real from steganographic images
- **Decoder**: Extracts hidden medical data from steganographic images

### 3. Loss Functions
- **Adversarial Loss**: GAN training objective
- **Reconstruction Loss**: Image quality preservation
- **Medical Data Loss**: Accurate data extraction
- **Perceptual Loss**: Visual quality enhancement

## Command Line Options

### Basic Usage
```bash
python scripts/start_training.py [OPTIONS]
```

### Options
- `--config`: Training configuration (`unified_real_fast` or `unified_real`)
- `--device`: Device to use (`cuda` or `cpu`)
- `--resume`: Resume from checkpoint path
- `--dry-run`: Show what would be done without training

### Examples
```bash
# Fast training on GPU
python scripts/start_training.py --config unified_real_fast --device cuda

# Full training on CPU (not recommended)
python scripts/start_training.py --config unified_real --device cpu

# Resume training from checkpoint
python scripts/start_training.py --resume models/unified_real_fast/checkpoint_epoch_50.pth

# Dry run to check setup
python scripts/start_training.py --dry-run
```

## Monitoring and Evaluation

### TensorBoard Monitoring
```bash
# Start TensorBoard
tensorboard --logdir results/logs

# View specific configuration
tensorboard --logdir results/logs/unified_real_fast
```

### Key Metrics to Monitor
- **Generator Loss**: Should decrease over time
- **Discriminator Loss**: Should stabilize around 0.5-0.7
- **Data Extraction Accuracy**: Should increase toward 1.0
- **PSNR/SSIM**: Image quality metrics

### Checkpoints
- Saved every 5-10 epochs (depending on configuration)
- Best model saved automatically based on validation loss
- Location: `models/{config_name}/`

## Hardware Requirements

### Recommended Specifications
- **GPU**: NVIDIA GPU with 8GB+ VRAM (RTX 3070 or better)
- **RAM**: 16GB+ system memory
- **Storage**: 10GB+ free space for models and logs

### Performance Optimization
- Use CUDA if available (10-50x faster than CPU)
- Reduce batch size if running out of GPU memory
- Use mixed precision training for faster training

## Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
```bash
# Reduce batch size
python scripts/start_training.py --config unified_real_fast
# (unified_real_fast uses smaller batch size)
```

#### 2. Dataset Not Found
- Ensure unified dataset exists in `data/unified_medical_real/`
- Check medical images are in `data/medical_images/`

#### 3. Slow Training
- Verify CUDA is being used: check "Using device: cuda" in logs
- Reduce number of workers if CPU is bottleneck
- Use SSD storage for faster data loading

### Performance Tips
1. **Start with fast configuration** for initial testing
2. **Monitor GPU utilization** with `nvidia-smi`
3. **Use TensorBoard** to track training progress
4. **Save checkpoints frequently** to avoid losing progress

## Integration with Additional Data

### Adding New Medical Records
1. Add records to `data/unified_medical_real/unified_real_train_records.json`
2. Ensure records follow either MIMIC-IV or compact medical format
3. Restart training (system will automatically detect new data)

### Adding New Medical Images
1. Add images to appropriate modality folder in `data/medical_images/`
2. Ensure images are in PNG format and reasonable quality
3. System will automatically include new images in training

## Next Steps After Training

### 1. Model Evaluation
```bash
# Run comprehensive demo with trained model
python scripts/comprehensive_demo.py --checkpoint models/unified_real_fast/best_checkpoint.pth
```

### 2. Testing with New Data
```bash
# Test with specific medical record
python scripts/demo.py --checkpoint models/unified_real_fast/best_checkpoint.pth --medical_data your_data.json
```

### 3. Production Deployment
- Use best checkpoint for inference
- Implement security measures for medical data
- Consider additional validation with clinical experts

## Support and Documentation

- **Training Logs**: Check `results/logs/` for detailed training information
- **Model Checkpoints**: Available in `models/` directory
- **Configuration Files**: See `config/training_config.py` for all options
- **Dataset Documentation**: Refer to `MIMIC_IV_INTEGRATION_REPORT.md`

For questions or issues, refer to the comprehensive demo and evaluation scripts to validate your trained models.

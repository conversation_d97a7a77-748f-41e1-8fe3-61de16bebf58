{"analysis_timestamp": "2025-05-26T07:08:43.107343", "mimic_path": "mimic-iv-clinical-database-demo-2.2", "synthetic_data_path": "data\\medical_samples\\compact_sample_records.json", "structure_analysis": {"root_path": "mimic-iv-clinical-database-demo-2.2", "directories": ["hosp", "icu"], "files": {"demo_subject_id.csv": {"size_bytes": 911, "size_mb": 0.0, "extension": ".csv"}, "LICENSE.txt": {"size_bytes": 25815, "size_mb": 0.02, "extension": ".txt"}, "README.txt": {"size_bytes": 978, "size_mb": 0.0, "extension": ".txt"}, "SHA256SUMS.txt": {"size_bytes": 2966, "size_mb": 0.0, "extension": ".txt"}, "hosp\\admissions.csv": {"size_bytes": 47370, "size_mb": 0.05, "extension": ".csv"}, "hosp\\admissions.csv.gz": {"size_bytes": 11072, "size_mb": 0.01, "extension": ".gz"}, "hosp\\diagnoses_icd.csv": {"size_bytes": 128649, "size_mb": 0.12, "extension": ".csv"}, "hosp\\diagnoses_icd.csv.gz": {"size_bytes": 24198, "size_mb": 0.02, "extension": ".gz"}, "hosp\\drgcodes.csv": {"size_bytes": 32662, "size_mb": 0.03, "extension": ".csv"}, "hosp\\drgcodes.csv.gz": {"size_bytes": 7459, "size_mb": 0.01, "extension": ".gz"}, "hosp\\d_hcpcs.csv": {"size_bytes": 3330541, "size_mb": 3.18, "extension": ".csv"}, "hosp\\d_hcpcs.csv.gz": {"size_bytes": 510070, "size_mb": 0.49, "extension": ".gz"}, "hosp\\d_icd_diagnoses.csv": {"size_bytes": 8849535, "size_mb": 8.44, "extension": ".csv"}, "hosp\\d_icd_diagnoses.csv.gz": {"size_bytes": 1813533, "size_mb": 1.73, "extension": ".gz"}, "hosp\\d_icd_procedures.csv": {"size_bytes": 7340791, "size_mb": 7.0, "extension": ".csv"}, "hosp\\d_icd_procedures.csv.gz": {"size_bytes": 1082613, "size_mb": 1.03, "extension": ".gz"}, "hosp\\d_labitems.csv": {"size_bytes": 63564, "size_mb": 0.06, "extension": ".csv"}, "hosp\\d_labitems.csv.gz": {"size_bytes": 13520, "size_mb": 0.01, "extension": ".gz"}, "hosp\\emar.csv": {"size_bytes": 5236096, "size_mb": 4.99, "extension": ".csv"}, "hosp\\emar.csv.gz": {"size_bytes": 734347, "size_mb": 0.7, "extension": ".gz"}, "hosp\\emar_detail.csv": {"size_bytes": 7237418, "size_mb": 6.9, "extension": ".csv"}, "hosp\\emar_detail.csv.gz": {"size_bytes": 692290, "size_mb": 0.66, "extension": ".gz"}, "hosp\\hcpcsevents.csv": {"size_bytes": 3889, "size_mb": 0.0, "extension": ".csv"}, "hosp\\hcpcsevents.csv.gz": {"size_bytes": 963, "size_mb": 0.0, "extension": ".gz"}, "hosp\\labevents.csv": {"size_bytes": 12265209, "size_mb": 11.7, "extension": ".csv"}, "hosp\\labevents.csv.gz": {"size_bytes": 1963979, "size_mb": 1.87, "extension": ".gz"}, "hosp\\microbiologyevents.csv": {"size_bytes": 654177, "size_mb": 0.62, "extension": ".csv"}, "hosp\\microbiologyevents.csv.gz": {"size_bytes": 80991, "size_mb": 0.08, "extension": ".gz"}, "hosp\\omr.csv": {"size_bytes": 122419, "size_mb": 0.12, "extension": ".csv"}, "hosp\\omr.csv.gz": {"size_bytes": 17339, "size_mb": 0.02, "extension": ".gz"}, "hosp\\patients.csv": {"size_bytes": 3573, "size_mb": 0.0, "extension": ".csv"}, "hosp\\patients.csv.gz": {"size_bytes": 1083, "size_mb": 0.0, "extension": ".gz"}, "hosp\\pharmacy.csv": {"size_bytes": 3392282, "size_mb": 3.24, "extension": ".csv"}, "hosp\\pharmacy.csv.gz": {"size_bytes": 503956, "size_mb": 0.48, "extension": ".gz"}, "hosp\\poe.csv": {"size_bytes": 4441546, "size_mb": 4.24, "extension": ".csv"}, "hosp\\poe.csv.gz": {"size_bytes": 611200, "size_mb": 0.58, "extension": ".gz"}, "hosp\\poe_detail.csv": {"size_bytes": 235987, "size_mb": 0.23, "extension": ".csv"}, "hosp\\poe_detail.csv.gz": {"size_bytes": 26574, "size_mb": 0.03, "extension": ".gz"}, "hosp\\prescriptions.csv": {"size_bytes": 3126857, "size_mb": 2.98, "extension": ".csv"}, "hosp\\prescriptions.csv.gz": {"size_bytes": 606447, "size_mb": 0.58, "extension": ".gz"}, "hosp\\procedures_icd.csv": {"size_bytes": 28822, "size_mb": 0.03, "extension": ".csv"}, "hosp\\procedures_icd.csv.gz": {"size_bytes": 6602, "size_mb": 0.01, "extension": ".gz"}, "hosp\\provider.csv": {"size_bytes": 283568, "size_mb": 0.27, "extension": ".csv"}, "hosp\\provider.csv.gz": {"size_bytes": 151283, "size_mb": 0.14, "extension": ".gz"}, "hosp\\services.csv": {"size_bytes": 14203, "size_mb": 0.01, "extension": ".csv"}, "hosp\\services.csv.gz": {"size_bytes": 5072, "size_mb": 0.0, "extension": ".gz"}, "hosp\\transfers.csv": {"size_bytes": 101987, "size_mb": 0.1, "extension": ".csv"}, "hosp\\transfers.csv.gz": {"size_bytes": 23480, "size_mb": 0.02, "extension": ".gz"}, "icu\\caregiver.csv": {"size_bytes": 91160, "size_mb": 0.09, "extension": ".csv"}, "icu\\caregiver.csv.gz": {"size_bytes": 43441, "size_mb": 0.04, "extension": ".gz"}, "icu\\chartevents.csv": {"size_bytes": 64701023, "size_mb": 61.7, "extension": ".csv"}, "icu\\chartevents.csv.gz": {"size_bytes": 5549389, "size_mb": 5.29, "extension": ".gz"}, "icu\\datetimeevents.csv": {"size_bytes": 1669698, "size_mb": 1.59, "extension": ".csv"}, "icu\\datetimeevents.csv.gz": {"size_bytes": 119448, "size_mb": 0.11, "extension": ".gz"}, "icu\\d_items.csv": {"size_bytes": 368631, "size_mb": 0.35, "extension": ".csv"}, "icu\\d_items.csv.gz": {"size_bytes": 57073, "size_mb": 0.05, "extension": ".gz"}, "icu\\icustays.csv": {"size_bytes": 22297, "size_mb": 0.02, "extension": ".csv"}, "icu\\icustays.csv.gz": {"size_bytes": 5667, "size_mb": 0.01, "extension": ".gz"}, "icu\\ingredientevents.csv": {"size_bytes": 4485172, "size_mb": 4.28, "extension": ".csv"}, "icu\\ingredientevents.csv.gz": {"size_bytes": 601205, "size_mb": 0.57, "extension": ".gz"}, "icu\\inputevents.csv": {"size_bytes": 5343319, "size_mb": 5.1, "extension": ".csv"}, "icu\\inputevents.csv.gz": {"size_bytes": 788253, "size_mb": 0.75, "extension": ".gz"}, "icu\\outputevents.csv": {"size_bytes": 807957, "size_mb": 0.77, "extension": ".csv"}, "icu\\outputevents.csv.gz": {"size_bytes": 96413, "size_mb": 0.09, "extension": ".gz"}, "icu\\procedureevents.csv": {"size_bytes": 273914, "size_mb": 0.26, "extension": ".csv"}, "icu\\procedureevents.csv.gz": {"size_bytes": 44817, "size_mb": 0.04, "extension": ".gz"}}, "csv_files": {"demo_subject_id.csv": {"size_mb": 0.0, "compressed": false}, "hosp\\admissions.csv": {"size_mb": 0.05, "compressed": false}, "hosp\\admissions.csv.gz": {"size_mb": 0.01, "compressed": true}, "hosp\\diagnoses_icd.csv": {"size_mb": 0.12, "compressed": false}, "hosp\\diagnoses_icd.csv.gz": {"size_mb": 0.02, "compressed": true}, "hosp\\drgcodes.csv": {"size_mb": 0.03, "compressed": false}, "hosp\\drgcodes.csv.gz": {"size_mb": 0.01, "compressed": true}, "hosp\\d_hcpcs.csv": {"size_mb": 3.18, "compressed": false}, "hosp\\d_hcpcs.csv.gz": {"size_mb": 0.49, "compressed": true}, "hosp\\d_icd_diagnoses.csv": {"size_mb": 8.44, "compressed": false}, "hosp\\d_icd_diagnoses.csv.gz": {"size_mb": 1.73, "compressed": true}, "hosp\\d_icd_procedures.csv": {"size_mb": 7.0, "compressed": false}, "hosp\\d_icd_procedures.csv.gz": {"size_mb": 1.03, "compressed": true}, "hosp\\d_labitems.csv": {"size_mb": 0.06, "compressed": false}, "hosp\\d_labitems.csv.gz": {"size_mb": 0.01, "compressed": true}, "hosp\\emar.csv": {"size_mb": 4.99, "compressed": false}, "hosp\\emar.csv.gz": {"size_mb": 0.7, "compressed": true}, "hosp\\emar_detail.csv": {"size_mb": 6.9, "compressed": false}, "hosp\\emar_detail.csv.gz": {"size_mb": 0.66, "compressed": true}, "hosp\\hcpcsevents.csv": {"size_mb": 0.0, "compressed": false}, "hosp\\hcpcsevents.csv.gz": {"size_mb": 0.0, "compressed": true}, "hosp\\labevents.csv": {"size_mb": 11.7, "compressed": false}, "hosp\\labevents.csv.gz": {"size_mb": 1.87, "compressed": true}, "hosp\\microbiologyevents.csv": {"size_mb": 0.62, "compressed": false}, "hosp\\microbiologyevents.csv.gz": {"size_mb": 0.08, "compressed": true}, "hosp\\omr.csv": {"size_mb": 0.12, "compressed": false}, "hosp\\omr.csv.gz": {"size_mb": 0.02, "compressed": true}, "hosp\\patients.csv": {"size_mb": 0.0, "compressed": false}, "hosp\\patients.csv.gz": {"size_mb": 0.0, "compressed": true}, "hosp\\pharmacy.csv": {"size_mb": 3.24, "compressed": false}, "hosp\\pharmacy.csv.gz": {"size_mb": 0.48, "compressed": true}, "hosp\\poe.csv": {"size_mb": 4.24, "compressed": false}, "hosp\\poe.csv.gz": {"size_mb": 0.58, "compressed": true}, "hosp\\poe_detail.csv": {"size_mb": 0.23, "compressed": false}, "hosp\\poe_detail.csv.gz": {"size_mb": 0.03, "compressed": true}, "hosp\\prescriptions.csv": {"size_mb": 2.98, "compressed": false}, "hosp\\prescriptions.csv.gz": {"size_mb": 0.58, "compressed": true}, "hosp\\procedures_icd.csv": {"size_mb": 0.03, "compressed": false}, "hosp\\procedures_icd.csv.gz": {"size_mb": 0.01, "compressed": true}, "hosp\\provider.csv": {"size_mb": 0.27, "compressed": false}, "hosp\\provider.csv.gz": {"size_mb": 0.14, "compressed": true}, "hosp\\services.csv": {"size_mb": 0.01, "compressed": false}, "hosp\\services.csv.gz": {"size_mb": 0.0, "compressed": true}, "hosp\\transfers.csv": {"size_mb": 0.1, "compressed": false}, "hosp\\transfers.csv.gz": {"size_mb": 0.02, "compressed": true}, "icu\\caregiver.csv": {"size_mb": 0.09, "compressed": false}, "icu\\caregiver.csv.gz": {"size_mb": 0.04, "compressed": true}, "icu\\chartevents.csv": {"size_mb": 61.7, "compressed": false}, "icu\\chartevents.csv.gz": {"size_mb": 5.29, "compressed": true}, "icu\\datetimeevents.csv": {"size_mb": 1.59, "compressed": false}, "icu\\datetimeevents.csv.gz": {"size_mb": 0.11, "compressed": true}, "icu\\d_items.csv": {"size_mb": 0.35, "compressed": false}, "icu\\d_items.csv.gz": {"size_mb": 0.05, "compressed": true}, "icu\\icustays.csv": {"size_mb": 0.02, "compressed": false}, "icu\\icustays.csv.gz": {"size_mb": 0.01, "compressed": true}, "icu\\ingredientevents.csv": {"size_mb": 4.28, "compressed": false}, "icu\\ingredientevents.csv.gz": {"size_mb": 0.57, "compressed": true}, "icu\\inputevents.csv": {"size_mb": 5.1, "compressed": false}, "icu\\inputevents.csv.gz": {"size_mb": 0.75, "compressed": true}, "icu\\outputevents.csv": {"size_mb": 0.77, "compressed": false}, "icu\\outputevents.csv.gz": {"size_mb": 0.09, "compressed": true}, "icu\\procedureevents.csv": {"size_mb": 0.26, "compressed": false}, "icu\\procedureevents.csv.gz": {"size_mb": 0.04, "compressed": true}}, "total_size": 150928763, "total_size_mb": 143.94}, "csv_analysis": {"patients": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\patients.csv", "total_rows": 100, "columns": ["subject_id", "gender", "anchor_age", "anchor_year", "anchor_year_group", "dod"], "column_count": 6, "data_types": {"subject_id": "int64", "gender": "object", "anchor_age": "int64", "anchor_year": "int64", "anchor_year_group": "object", "dod": "object"}, "sample_data": {"subject_id": [10014729, 10003400, 10002428, 10032725, 10027445], "gender": ["F", "F", "F", "F", "F"], "anchor_age": [21, 72, 80, 38, 48], "anchor_year": [2125, 2134, 2155, 2143, 2142], "anchor_year_group": ["2011 - 2013", "2011 - 2013", "2011 - 2013", "2011 - 2013", "2011 - 2013"], "dod": ["2137-09-02", "2143-03-30", "2146-02-09", "2148-02-07", "2154-02-04"]}, "null_counts": {"subject_id": 0, "gender": 0, "anchor_age": 0, "anchor_year": 0, "anchor_year_group": 0, "dod": 69}, "unique_counts": {"subject_id": 100, "gender": 2, "anchor_age": 50, "anchor_year": 62, "anchor_year_group": 2, "dod": 31}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018776.86}, "anchor_age": {"min": 21.0, "max": 91.0, "mean": 61.75}, "anchor_year": {"min": 2110.0, "max": 2201.0, "mean": 2148.68}}}, "admissions": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\admissions.csv", "total_rows": 275, "columns": ["subject_id", "hadm_id", "admittime", "dischtime", "deathtime", "admission_type", "admit_provider_id", "admission_location", "discharge_location", "insurance", "language", "marital_status", "race", "edregtime", "edouttime", "hospital_expire_flag"], "column_count": 16, "data_types": {"subject_id": "int64", "hadm_id": "int64", "admittime": "object", "dischtime": "object", "deathtime": "object", "admission_type": "object", "admit_provider_id": "object", "admission_location": "object", "discharge_location": "object", "insurance": "object", "language": "object", "marital_status": "object", "race": "object", "edregtime": "object", "edouttime": "object", "hospital_expire_flag": "int64"}, "sample_data": {"subject_id": [10004235, 10009628, 10018081, 10006053, 10031404], "hadm_id": [24181354, 25926192, 23983182, 22942076, 21606243], "admittime": ["2196-02-24 14:38:00", "2153-09-17 17:08:00", "2134-08-18 02:02:00", "2111-11-13 23:39:00", "2113-08-04 18:46:00"], "dischtime": ["2196-03-04 14:02:00", "2153-09-25 13:20:00", "2134-08-23 19:35:00", "2111-11-15 17:20:00", "2113-08-06 20:57:00"], "deathtime": ["2111-11-15 17:20:00", "2115-10-12 22:20:00", "2177-03-29 14:15:00", "2137-09-02 17:05:00", "2185-01-22 14:25:00"], "admission_type": ["URGENT", "URGENT", "URGENT", "URGENT", "URGENT"], "admit_provider_id": ["P03YMR", "P41R5N", "P233F6", "P38TI6", "P07HDB"], "admission_location": ["TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL"], "discharge_location": ["SKILLED NURSING FACILITY", "HOME HEALTH CARE", "SKILLED NURSING FACILITY", "DIED", "HOME"], "insurance": ["Medicaid", "Medicaid", "Medicare", "Medicaid", "Other"], "language": ["ENGLISH", "?", "ENGLISH", "ENGLISH", "ENGLISH"], "marital_status": ["SINGLE", "MARRIED", "MARRIED", "WIDOWED", "MARRIED"], "race": ["BLACK/CAPE VERDEAN", "HISPANIC/LATINO - PUERTO RICAN", "WHITE", "UNKNOWN", "WHITE"], "edregtime": ["2196-02-24 12:15:00", "2134-08-17 16:24:00", "2119-10-26 06:00:00", "2192-09-23 22:43:00", "2148-06-29 21:06:00"], "edouttime": ["2196-02-24 17:07:00", "2134-08-18 03:15:00", "2119-10-26 06:37:00", "2192-09-24 09:38:00", "2148-06-30 02:27:00"], "hospital_expire_flag": [0, 0, 0, 1, 0]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "admittime": 0, "dischtime": 0, "deathtime": 260, "admission_type": 0, "admit_provider_id": 0, "admission_location": 0, "discharge_location": 42, "insurance": 0, "language": 0, "marital_status": 12, "race": 0, "edregtime": 93, "edouttime": 93, "hospital_expire_flag": 0}, "unique_counts": {"subject_id": 100, "hadm_id": 275, "admittime": 275, "dischtime": 275, "deathtime": 15, "admission_type": 9, "admit_provider_id": 170, "admission_location": 10, "discharge_location": 10, "insurance": 3, "language": 2, "marital_status": 4, "race": 14, "edregtime": 181, "edouttime": 181, "hospital_expire_flag": 2}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018901.541818181}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25130546.58909091}, "hospital_expire_flag": {"min": 0.0, "max": 1.0, "mean": 0.05454545454545454}}}, "diagnoses_icd": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\diagnoses_icd.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "seq_num", "icd_code", "icd_version"], "column_count": 5, "data_types": {"subject_id": "int64", "hadm_id": "int64", "seq_num": "int64", "icd_code": "object", "icd_version": "int64"}, "sample_data": {"subject_id": [10035185, 10035185, 10035185, 10035185, 10035185], "hadm_id": [22580999, 22580999, 22580999, 22580999, 22580999], "seq_num": [3, 10, 1, 9, 11], "icd_code": ["4139", "V707", "41401", "3899", "V8532"], "icd_version": [9, 9, 9, 9, 9]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "seq_num": 0, "icd_code": 0, "icd_version": 0}, "unique_counts": {"subject_id": 46, "hadm_id": 51, "seq_num": 21, "icd_code": 330, "icd_version": 2}, "value_ranges": {"subject_id": {"min": 10001217.0, "max": 10039831.0, "mean": 10018804.632}, "hadm_id": {"min": 20044587.0, "max": 29642388.0, "mean": 24727425.266}, "seq_num": {"min": 1.0, "max": 21.0, "mean": 6.286}, "icd_version": {"min": 9.0, "max": 10.0, "mean": 9.25}}}, "procedures_icd": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\procedures_icd.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "seq_num", "chartdate", "icd_code", "icd_version"], "column_count": 6, "data_types": {"subject_id": "int64", "hadm_id": "int64", "seq_num": "int64", "chartdate": "object", "icd_code": "object", "icd_version": "int64"}, "sample_data": {"subject_id": [10011398, 10011398, 10011398, 10014729, 10014729], "hadm_id": [27505812, 27505812, 27505812, 23300884, 23300884], "seq_num": [3, 2, 1, 4, 1], "chartdate": ["2146-12-15", "2146-12-15", "2146-12-15", "2125-03-23", "2125-03-20"], "icd_code": ["3961", "3615", "3614", "3897", "3403"], "icd_version": [9, 9, 9, 9, 9]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "seq_num": 0, "chartdate": 0, "icd_code": 0, "icd_version": 0}, "unique_counts": {"subject_id": 66, "hadm_id": 133, "seq_num": 23, "chartdate": 278, "icd_code": 271, "icd_version": 2}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10039997.0, "mean": 10020222.456}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25422480.832}, "seq_num": {"min": 1.0, "max": 23.0, "mean": 4.144}, "icd_version": {"min": 9.0, "max": 10.0, "mean": 9.45}}}, "prescriptions": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\prescriptions.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "pharmacy_id", "poe_id", "poe_seq", "order_provider_id", "starttime", "stoptime", "drug_type", "drug", "formulary_drug_cd", "gsn", "ndc", "prod_strength", "form_rx", "dose_val_rx", "dose_unit_rx", "form_val_disp", "form_unit_disp", "doses_per_24_hrs", "route"], "column_count": 21, "data_types": {"subject_id": "int64", "hadm_id": "int64", "pharmacy_id": "int64", "poe_id": "object", "poe_seq": "float64", "order_provider_id": "object", "starttime": "object", "stoptime": "object", "drug_type": "object", "drug": "object", "formulary_drug_cd": "object", "gsn": "object", "ndc": "float64", "prod_strength": "object", "form_rx": "object", "dose_val_rx": "float64", "dose_unit_rx": "object", "form_val_disp": "float64", "form_unit_disp": "object", "doses_per_24_hrs": "float64", "route": "object"}, "sample_data": {"subject_id": [10027602, 10027602, 10027602, 10027602, 10027602], "hadm_id": [28166872, 28166872, 28166872, 28166872, 28166872], "pharmacy_id": [27168639, 40720238, 62845687, 24340150, 14435820], "poe_id": ["10023239-119", "10023239-284", "10023239-308", "10020740-830", "10005817-17"], "poe_seq": [119.0, 284.0, 308.0, 830.0, 17.0], "order_provider_id": ["P45MRP", "P820BX", "P26UJY", "P451D0", "P146T2"], "starttime": ["2201-10-30 12:00:00", "2201-10-30 12:00:00", "2201-10-31 12:00:00", "2201-10-30 12:00:00", "2201-10-30 12:00:00"], "stoptime": ["2137-06-22 19:00:00", "2140-10-06 16:00:00", "2140-10-08 19:00:00", "2150-04-08 22:00:00", "2132-12-13 12:00:00"], "drug_type": ["MAIN", "MAIN", "MAIN", "MAIN", "MAIN"], "drug": ["Fentanyl Citrate", "Fentanyl Citrate", "Lorazepam", "Midazolam", "Midazolam"], "formulary_drug_cd": ["FENT2I", "FENT2I", "LORA2I", "MIDA2I", "MIDA2I"], "gsn": ["027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 ", "027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 ", "027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 ", "001723", "001723"], "ndc": [2821501.0, 2821501.0, 2821501.0, 2821501.0, 2821501.0], "prod_strength": ["100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial"], "form_rx": ["TAB", "TAB", "CAP", "TAB", "TAB"], "dose_val_rx": [0.0, 0.0, 0.0, 0.0, 0.0], "dose_unit_rx": ["UNIT", "UNIT", "UNIT", "UNIT", "UNIT"], "form_val_disp": [0.0, 0.0, 0.0, 0.0, 0.0], "form_unit_disp": ["mL", "mL", "mL", "mL", "mL"], "doses_per_24_hrs": [1.0, 0.0, 1.0, 0.0, 1.0], "route": ["SC", "SC", "SC", "SC", "SC"]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "pharmacy_id": 0, "poe_id": 7, "poe_seq": 7, "order_provider_id": 9, "starttime": 0, "stoptime": 6, "drug_type": 0, "drug": 0, "formulary_drug_cd": 5, "gsn": 74, "ndc": 14, "prod_strength": 9, "form_rx": 495, "dose_val_rx": 9, "dose_unit_rx": 9, "form_val_disp": 9, "form_unit_disp": 9, "doses_per_24_hrs": 308, "route": 6}, "unique_counts": {"subject_id": 86, "hadm_id": 175, "pharmacy_id": 499, "poe_id": 480, "poe_seq": 395, "order_provider_id": 275, "starttime": 463, "stoptime": 408, "drug_type": 2, "drug": 25, "formulary_drug_cd": 24, "gsn": 19, "ndc": 17, "prod_strength": 19, "form_rx": 2, "dose_val_rx": 9, "dose_unit_rx": 11, "form_val_disp": 2, "form_unit_disp": 8, "doses_per_24_hrs": 4, "route": 8}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10021156.47}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25289121.328}, "pharmacy_id": {"min": 408384.0, "max": 99787861.0, "mean": 51121759.838}, "poe_seq": {"min": 7.0, "max": 3274.0, "mean": 540.7667342799189}, "ndc": {"min": 0.0, "max": 67457033950.0, "mean": **********.09465}, "dose_val_rx": {"min": 0.0, "max": 125.0, "mean": 1.0132382892057026}, "form_val_disp": {"min": 0.0, "max": 1.0, "mean": 0.4134419551934827}, "doses_per_24_hrs": {"min": 0.0, "max": 3.0, "mean": 1.109375}}}, "labevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\labevents.csv", "total_rows": 500, "columns": ["labevent_id", "subject_id", "hadm_id", "specimen_id", "itemid", "order_provider_id", "charttime", "storetime", "value", "valuenum", "valueuom", "ref_range_lower", "ref_range_upper", "flag", "priority", "comments"], "column_count": 16, "data_types": {"labevent_id": "int64", "subject_id": "int64", "hadm_id": "float64", "specimen_id": "int64", "itemid": "int64", "order_provider_id": "float64", "charttime": "object", "storetime": "object", "value": "object", "valuenum": "float64", "valueuom": "object", "ref_range_lower": "float64", "ref_range_upper": "float64", "flag": "object", "priority": "object", "comments": "object"}, "sample_data": {"labevent_id": [172061, 172062, 172068, 172063, 172050], "subject_id": [10014354, 10014354, 10014354, 10014354, 10014354], "hadm_id": [29600294.0, 29600294.0, 29600294.0, 29600294.0, 29600294.0], "specimen_id": [1808066, 1808066, 1808066, 1808066, 1808066], "itemid": [51277, 51279, 52172, 51301, 51249], "order_provider_id": [], "charttime": ["2148-08-16 00:00:00", "2148-08-16 00:00:00", "2148-08-16 00:00:00", "2148-08-16 00:00:00", "2148-08-16 00:00:00"], "storetime": ["2148-08-16 01:30:00", "2148-08-16 01:30:00", "2148-08-16 01:30:00", "2148-08-16 01:30:00", "2148-08-16 01:30:00"], "value": ["15.4", "3.35", "49.7", "20.3", "31.1"], "valuenum": [15.4, 3.35, 49.7, 20.3, 31.1], "valueuom": ["%", "m/uL", "fL", "K/uL", "g/dL"], "ref_range_lower": [10.5, 4.6, 35.1, 4.0, 32.0], "ref_range_upper": [15.5, 6.1, 46.3, 10.0, 37.0], "flag": ["abnormal", "abnormal", "abnormal", "abnormal", "abnormal"], "priority": ["ROUTINE", "ROUTINE", "ROUTINE", "ROUTINE", "ROUTINE"], "comments": ["New reference range as of ___.", "Using this patient's age, gender, and serum creatinine value of 1.8, .  estimated GFR (eGFR) is likely between 39 and 47 mL/min/1.73 m2, .  provided the serum creatinine value is stable. .  (Patients with more muscle mass and better nutritional status are more .  likely to be at the higher end of this range.) .  An eGFR < 60 suggests kidney disease in those below the age of 65 .  and there may be kidney disease in those over 65..", "New reference range as of ___.", "If fasting, 70-100 normal, >125 provisional diabetes.", "NORMAL."]}, "null_counts": {"labevent_id": 0, "subject_id": 0, "hadm_id": 8, "specimen_id": 0, "itemid": 0, "order_provider_id": 500, "charttime": 0, "storetime": 1, "value": 63, "valuenum": 65, "valueuom": 85, "ref_range_lower": 85, "ref_range_upper": 85, "flag": 330, "priority": 0, "comments": 411}, "unique_counts": {"labevent_id": 500, "subject_id": 6, "hadm_id": 9, "specimen_id": 33, "itemid": 82, "order_provider_id": 0, "charttime": 14, "storetime": 49, "value": 241, "valuenum": 243, "valueuom": 13, "ref_range_lower": 51, "ref_range_upper": 55, "flag": 1, "priority": 2, "comments": 22}, "value_ranges": {"labevent_id": {"min": 96447.0, "max": 415836.0, "mean": 316810.876}, "subject_id": {"min": 10007818.0, "max": 10035631.0, "mean": 10027097.194}, "hadm_id": {"min": 20385771.0, "max": 29974575.0, "mean": 24698991.39227642}, "specimen_id": {"min": 1808066.0, "max": 96405107.0, "mean": 38798810.46}, "itemid": {"min": 50861.0, "max": 52172.0, "mean": 51167.392}, "order_provider_id": {"min": null, "max": null, "mean": null}, "valuenum": {"min": 0.0, "max": 1550.0, "mean": 41.756659770114936}, "ref_range_lower": {"min": 0.0, "max": 180.0, "mean": 26.1449421686747}, "ref_range_upper": {"min": 0.0, "max": 440.0, "mean": 47.90659036144579}}}, "chartevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\chartevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "charttime", "storetime", "itemid", "value", "valuenum", "valueuom", "warning"], "column_count": 11, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "int64", "charttime": "object", "storetime": "object", "itemid": "int64", "value": "object", "valuenum": "float64", "valueuom": "object", "warning": "int64"}, "sample_data": {"subject_id": [10005817, 10005817, 10005817, 10005817, 10005817], "hadm_id": [20626031, 20626031, 20626031, 20626031, 20626031], "stay_id": [32604416, 32604416, 32604416, 32604416, 32604416], "caregiver_id": [6770, 6770, 6770, 6770, 6770], "charttime": ["2132-12-16 00:00:00", "2132-12-16 00:00:00", "2132-12-16 00:00:00", "2132-12-16 00:00:00", "2132-12-16 00:00:00"], "storetime": ["2132-12-15 23:45:00", "2132-12-15 23:43:00", "2132-12-15 23:47:00", "2132-12-15 23:47:00", "2132-12-15 23:45:00"], "itemid": [225054, 223769, 223956, 224866, 227341], "value": ["On ", "100", "Atrial demand", "Yes", "No"], "valuenum": [100.0, 0.0, 52.0, 55.0, 0.0], "valueuom": ["%", "bpm", "bpm", "mmHg", "mV"], "warning": [0, 0, 0, 0, 0]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 0, "charttime": 0, "storetime": 0, "itemid": 0, "value": 12, "valuenum": 270, "valueuom": 337, "warning": 0}, "unique_counts": {"subject_id": 1, "hadm_id": 1, "stay_id": 1, "caregiver_id": 3, "charttime": 13, "storetime": 47, "itemid": 186, "value": 206, "valuenum": 92, "valueuom": 9, "warning": 1}, "value_ranges": {"subject_id": {"min": 10005817.0, "max": 10005817.0, "mean": 10005817.0}, "hadm_id": {"min": 20626031.0, "max": 20626031.0, "mean": 20626031.0}, "stay_id": {"min": 32604416.0, "max": 32604416.0, "mean": 32604416.0}, "caregiver_id": {"min": 6770.0, "max": 82315.0, "mean": 48476.394}, "itemid": {"min": 220045.0, "max": 228868.0, "mean": 223642.682}, "valuenum": {"min": 0.0, "max": 294.0, "mean": 37.99695652173913}, "warning": {"min": 0.0, "max": 0.0, "mean": 0.0}}}, "transfers": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\transfers.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "transfer_id", "eventtype", "careunit", "intime", "outtime"], "column_count": 7, "data_types": {"subject_id": "int64", "hadm_id": "float64", "transfer_id": "int64", "eventtype": "object", "careunit": "object", "intime": "object", "outtime": "object"}, "sample_data": {"subject_id": [10009049, 10025612, 10020786, 10014078, 10039831], "hadm_id": [22995465.0, 23403708.0, 23488445.0, 25809882.0, 26924951.0], "transfer_id": [30030230, 32533329, 37922399, 34694622, 37155928], "eventtype": ["discharge", "discharge", "discharge", "discharge", "discharge"], "careunit": ["Med/Surg", "Med/Surg", "Med/Surg", "Medicine", "Medicine"], "intime": ["2174-05-31 14:21:47", "2125-10-03 12:25:27", "2189-06-13 17:25:44", "2166-08-26 14:49:42", "2116-01-02 14:35:02"], "outtime": ["2154-01-09 11:53:26", "2125-10-03 12:25:27", "2129-04-11 17:45:53", "2131-03-13 17:01:34", "2160-11-11 11:40:33"]}, "null_counts": {"subject_id": 0, "hadm_id": 24, "transfer_id": 0, "eventtype": 0, "careunit": 110, "intime": 0, "outtime": 110}, "unique_counts": {"subject_id": 60, "hadm_id": 110, "transfer_id": 500, "eventtype": 4, "careunit": 25, "intime": 500, "outtime": 390}, "value_ranges": {"subject_id": {"min": 10001217.0, "max": 10039831.0, "mean": 10017372.064}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 24646825.911764707}, "transfer_id": {"min": 30014085.0, "max": 39997902.0, "mean": 35100254.406}}}, "demo_subject_id": {"file_path": "mimic-iv-clinical-database-demo-2.2\\demo_subject_id.csv", "total_rows": 100, "columns": ["subject_id"], "column_count": 1, "data_types": {"subject_id": "int64"}, "sample_data": {"subject_id": [10000032, 10001217, 10001725, 10002428, 10002495]}, "null_counts": {"subject_id": 0}, "unique_counts": {"subject_id": 100}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018776.86}}}, "drgcodes": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\drgcodes.csv", "total_rows": 454, "columns": ["subject_id", "hadm_id", "drg_type", "drg_code", "description", "drg_severity", "drg_mortality"], "column_count": 7, "data_types": {"subject_id": "int64", "hadm_id": "int64", "drg_type": "object", "drg_code": "int64", "description": "object", "drg_severity": "float64", "drg_mortality": "float64"}, "sample_data": {"subject_id": [10004235, 10026255, 10032725, 10005866, 10008454], "hadm_id": [22187210, 22059910, 20611640, 21636229, 20291550], "drg_type": ["HCFA", "HCFA", "HCFA", "HCFA", "HCFA"], "drg_code": [864, 180, 54, 393, 956], "description": ["FEVER", "RESPIRATORY NEOPLASMS W MCC", "NERVOUS SYSTEM NEOPLASMS W MCC", "OTHER DIGESTIVE SYSTEM DIAGNOSES W MCC", "LIMB REATTACHMENT, HIP & FEMUR PROC FOR M<PERSON>LT<PERSON>LE SIGNIFICANT TRAUMA"], "drg_severity": [3.0, 2.0, 2.0, 2.0, 1.0], "drg_mortality": [1.0, 1.0, 1.0, 1.0, 1.0]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "drg_type": 0, "drg_code": 0, "description": 0, "drg_severity": 233, "drg_mortality": 233}, "unique_counts": {"subject_id": 100, "hadm_id": 233, "drg_type": 2, "drg_code": 240, "description": 259, "drg_severity": 4, "drg_mortality": 4}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10019337.114537446}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25168178.45154185}, "drg_code": {"min": 14.0, "max": 988.0, "mean": 391.88546255506606}, "drg_severity": {"min": 1.0, "max": 4.0, "mean": 2.8054298642533935}, "drg_mortality": {"min": 1.0, "max": 4.0, "mean": 2.4705882352941178}}}, "d_hcpcs": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_hcpcs.csv", "total_rows": 500, "columns": ["code", "category", "long_description", "short_description"], "column_count": 4, "data_types": {"code": "object", "category": "float64", "long_description": "object", "short_description": "object"}, "sample_data": {"code": ["TD   ", "A0428", "V5272", "S2080", "S8037"], "category": [], "long_description": ["Rn", "Ambulance service, basic life support, non-emergency transport, (bls)", "Assistive listening device, tdd", "Laser-assisted uvulopalatoplasty (laup)", "Magnetic resonance cholangiopancreatography (mrcp)"], "short_description": ["Rn", "Bls", "Tdd", "<PERSON><PERSON>", "Mrcp"]}, "null_counts": {"code": 0, "category": 500, "long_description": 4, "short_description": 0}, "unique_counts": {"code": 500, "category": 0, "long_description": 492, "short_description": 491}, "value_ranges": {"category": {"min": null, "max": null, "mean": null}}}, "d_icd_diagnoses": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_icd_diagnoses.csv", "total_rows": 500, "columns": ["icd_code", "icd_version", "long_title"], "column_count": 3, "data_types": {"icd_code": "object", "icd_version": "int64", "long_title": "object"}, "sample_data": {"icd_code": ["0090", "01160", "01186", "01200", "01236"], "icd_version": [9, 9, 9, 9, 9], "long_title": ["Infectious colitis, enteritis, and gastroenteritis", "Tuberculous pneumonia [any form], unspecified", "Other specified pulmonary tuberculosis, tubercle bacilli not found by bacteriological or histological examination, but tuberculosis confirmed by other methods [inoculation of animals]", "Tuberculous pleurisy, unspecified", "Tuberculous laryngitis, tubercle bacilli not found by bacteriological or histological examination, but tuberculosis confirmed by other methods [inoculation of animals]"]}, "null_counts": {"icd_code": 0, "icd_version": 0, "long_title": 0}, "unique_counts": {"icd_code": 500, "icd_version": 1, "long_title": 500}, "value_ranges": {"icd_version": {"min": 9.0, "max": 9.0, "mean": 9.0}}}, "d_icd_procedures": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_icd_procedures.csv", "total_rows": 500, "columns": ["icd_code", "icd_version", "long_title"], "column_count": 3, "data_types": {"icd_code": "int64", "icd_version": "int64", "long_title": "object"}, "sample_data": {"icd_code": [39, 48, 74, 77, 126], "icd_version": [9, 9, 9, 9, 9], "long_title": ["Other computer assisted surgery", "Insertion of four or more vascular stents", "Hip bearing surface, metal-on-polyethylene", "Hip bearing surface, ceramic-on-polyethylene", "Insertion of catheter(s) into cranial cavity or tissue"]}, "null_counts": {"icd_code": 0, "icd_version": 0, "long_title": 0}, "unique_counts": {"icd_code": 500, "icd_version": 1, "long_title": 500}, "value_ranges": {"icd_code": {"min": 3.0, "max": 9985.0, "mean": 5306.906}, "icd_version": {"min": 9.0, "max": 9.0, "mean": 9.0}}}, "d_labitems": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_labitems.csv", "total_rows": 500, "columns": ["itemid", "label", "fluid", "category"], "column_count": 4, "data_types": {"itemid": "int64", "label": "object", "fluid": "object", "category": "object"}, "sample_data": {"itemid": [50808, 50826, 50813, 52029, 50801], "label": ["Free Calcium", "Tidal Volume", "Lactate", "% Ionized Calcium", "Alveolar-arterial Gradient"], "fluid": ["Blood", "Blood", "Blood", "Blood", "Blood"], "category": ["Blood Gas", "Blood Gas", "Blood Gas", "Blood Gas", "Blood Gas"]}, "null_counts": {"itemid": 0, "label": 1, "fluid": 0, "category": 0}, "unique_counts": {"itemid": 500, "label": 448, "fluid": 5, "category": 2}, "value_ranges": {"itemid": {"min": 50801.0, "max": 53154.0, "mean": 51533.85}}}, "emar": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\emar.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "emar_id", "emar_seq", "poe_id", "pharmacy_id", "enter_provider_id", "charttime", "medication", "event_txt", "scheduletime", "storetime"], "column_count": 12, "data_types": {"subject_id": "int64", "hadm_id": "float64", "emar_id": "object", "emar_seq": "int64", "poe_id": "object", "pharmacy_id": "float64", "enter_provider_id": "object", "charttime": "object", "medication": "object", "event_txt": "object", "scheduletime": "object", "storetime": "object"}, "sample_data": {"subject_id": [10005909, 10005909, 10008287, 10010471, 10015272], "hadm_id": [20199380.0, 20199380.0, 22168393.0, 21322534.0, 27993466.0], "emar_id": ["10005909-74", "10005909-79", "10008287-32", "10010471-33", "10015272-31"], "emar_seq": [74, 79, 32, 33, 31], "poe_id": ["10005909-97", "10005909-97", "10008287-58", "10010471-51", "10015272-48"], "pharmacy_id": [96110427.0, 96110427.0, 52131847.0, 88758875.0, 25652531.0], "enter_provider_id": ["P26PKF", "P33K2X", "P54TSS", "P851DG", "P14CSQ"], "charttime": ["2144-10-31 05:56:00", "2144-10-31 08:00:00", "2145-09-28 20:15:00", "2155-05-08 21:45:00", "2137-06-13 08:36:00"], "medication": ["Magnesium Sulfate", "Magnesium Sulfate", "Potassium Chloride Replacement (Critical Care and Oncology)", "Metoprolol Tartrate", "Metoprolol Tartrate"], "event_txt": ["Read", "Applied", "Applied", "Applied", "Applied"], "scheduletime": ["2144-10-31 05:56:00", "2144-10-31 08:00:00", "2145-09-28 20:15:00", "2155-05-08 21:45:00", "2137-06-13 08:36:00"], "storetime": ["2144-10-31 05:56:00", "2144-10-31 08:15:00", "2145-09-28 20:38:00", "2155-05-08 22:40:00", "2137-06-13 08:36:00"]}, "null_counts": {"subject_id": 0, "hadm_id": 12, "emar_id": 0, "emar_seq": 0, "poe_id": 0, "pharmacy_id": 30, "enter_provider_id": 451, "charttime": 0, "medication": 4, "event_txt": 311, "scheduletime": 0, "storetime": 0}, "unique_counts": {"subject_id": 39, "hadm_id": 79, "emar_id": 500, "emar_seq": 434, "poe_id": 190, "pharmacy_id": 170, "enter_provider_id": 29, "charttime": 486, "medication": 42, "event_txt": 3, "scheduletime": 486, "storetime": 485}, "value_ranges": {"subject_id": {"min": 10004235.0, "max": 10040025.0, "mean": 10022651.682}, "hadm_id": {"min": 20199380.0, "max": 29842315.0, "mean": 25344611.782786883}, "emar_seq": {"min": 7.0, "max": 3855.0, "mean": 825.726}, "pharmacy_id": {"min": 1854122.0, "max": 99158827.0, "mean": 51289754.64042553}}}, "emar_detail": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\emar_detail.csv", "total_rows": 500, "columns": ["subject_id", "emar_id", "emar_seq", "parent_field_ordinal", "administration_type", "pharmacy_id", "barcode_type", "reason_for_no_barcode", "complete_dose_not_given", "dose_due", "dose_due_unit", "dose_given", "dose_given_unit", "will_remainder_of_dose_be_given", "product_amount_given", "product_unit", "product_code", "product_description", "product_description_other", "prior_infusion_rate", "infusion_rate", "infusion_rate_adjustment", "infusion_rate_adjustment_amount", "infusion_rate_unit", "route", "infusion_complete", "completion_interval", "new_iv_bag_hung", "continued_infusion_in_other_location", "restart_interval", "side", "site", "non_formulary_visual_verification"], "column_count": 33, "data_types": {"subject_id": "int64", "emar_id": "object", "emar_seq": "int64", "parent_field_ordinal": "float64", "administration_type": "object", "pharmacy_id": "float64", "barcode_type": "object", "reason_for_no_barcode": "object", "complete_dose_not_given": "object", "dose_due": "float64", "dose_due_unit": "object", "dose_given": "object", "dose_given_unit": "object", "will_remainder_of_dose_be_given": "object", "product_amount_given": "float64", "product_unit": "object", "product_code": "float64", "product_description": "object", "product_description_other": "object", "prior_infusion_rate": "float64", "infusion_rate": "float64", "infusion_rate_adjustment": "object", "infusion_rate_adjustment_amount": "float64", "infusion_rate_unit": "object", "route": "float64", "infusion_complete": "object", "completion_interval": "object", "new_iv_bag_hung": "object", "continued_infusion_in_other_location": "float64", "restart_interval": "object", "side": "float64", "site": "float64", "non_formulary_visual_verification": "float64"}, "sample_data": {"subject_id": [10039708, 10015860, 10005866, 10014354, 10029291], "emar_id": ["10039708-1750", "10015860-565", "10005866-571", "10014354-2295", "10029291-140"], "emar_seq": [1750, 565, 571, 2295, 140], "parent_field_ordinal": [1.1, 1.1, 1.1, 1.1, 1.1], "administration_type": ["IV Infusion", "IV Infusion", "Insulin Infusion (IV Drip)", "IV Infusion", "IV Infusion"], "pharmacy_id": [80400800.0, 98178739.0, 7889909.0, 44116031.0, 56470980.0], "barcode_type": ["bc", "pmto", "bc", "bc", "pmto"], "reason_for_no_barcode": ["Discarded Packaging", "Given in O.R. Holding", "Given in O.R. Holding", "Barcode Damaged", "___"], "complete_dose_not_given": ["No", "No", "No", "No", "No"], "dose_due": [], "dose_due_unit": ["mcg/kg/min", "mcg/hr", "UNIT/HR", "mcg/kg/min", "mcg/kg/min"], "dose_given": ["0.25", "___", "50", "3", "500"], "dose_given_unit": ["mg", "mL/hr", "mcg", "mL", "mg"], "will_remainder_of_dose_be_given": ["No", "Yes", "No", "No", "Yes"], "product_amount_given": [250.0, 100.0, 100.0, 10.0, 1000.0], "product_unit": ["mL", "mL", "mL", "mL", "mL"], "product_code": [], "product_description": ["Potassium Phosphate 15 mmol in 250 mL 0.9% Sodium Chloride ", "venetoclax 10 mg tablet", "LeVETiracetam 500 mg in 100 mL 0.9% Sodium Chloride", "LeVETiracetam 500 mg in 100 mL 0.9% Sodium Chloride", "Venetoclax 100 mg"], "product_description_other": ["Insulin - <PERSON><PERSON><PERSON>", "Insulin - <PERSON><PERSON><PERSON>", "Insulin - Insulin Regular", "Insulin - <PERSON><PERSON><PERSON>", "Insulin - <PERSON><PERSON><PERSON>"], "prior_infusion_rate": [20.0, 41.6, 66.7, 66.7, 20.0], "infusion_rate": [10.0, 25.0, 41.6, 66.7, 66.7], "infusion_rate_adjustment": ["See Flowsheet for Titration", "See Flowsheet for Titration", "See Flowsheet for Titration", "See Flowsheet for Titration", "See Flowsheet for Titration"], "infusion_rate_adjustment_amount": [], "infusion_rate_unit": ["mcg/hr", "UNIT/HR", "mcg/kg/min", "mcg/hr", "mcg/kg/min"], "route": [], "infusion_complete": ["N", "Y", "N", "Y", "Y"], "completion_interval": ["within 4 hours", "within 2 hours", "within 2 hours", "within 4 hours", "within 4 hours"], "new_iv_bag_hung": ["Y", "Y", "Y", "Y", "Y"], "continued_infusion_in_other_location": [], "restart_interval": ["PRN", "PRN", "PRN", "PRN", "PRN"], "side": [], "site": [], "non_formulary_visual_verification": []}, "null_counts": {"subject_id": 0, "emar_id": 0, "emar_seq": 0, "parent_field_ordinal": 262, "administration_type": 239, "pharmacy_id": 297, "barcode_type": 442, "reason_for_no_barcode": 473, "complete_dose_not_given": 471, "dose_due": 500, "dose_due_unit": 304, "dose_given": 310, "dose_given_unit": 306, "will_remainder_of_dose_be_given": 420, "product_amount_given": 463, "product_unit": 467, "product_code": 500, "product_description": 458, "product_description_other": 495, "prior_infusion_rate": 486, "infusion_rate": 483, "infusion_rate_adjustment": 382, "infusion_rate_adjustment_amount": 500, "infusion_rate_unit": 379, "route": 500, "infusion_complete": 484, "completion_interval": 494, "new_iv_bag_hung": 477, "continued_infusion_in_other_location": 500, "restart_interval": 493, "side": 500, "site": 500, "non_formulary_visual_verification": 500}, "unique_counts": {"subject_id": 47, "emar_id": 495, "emar_seq": 417, "parent_field_ordinal": 1, "administration_type": 7, "pharmacy_id": 182, "barcode_type": 3, "reason_for_no_barcode": 6, "complete_dose_not_given": 1, "dose_due": 0, "dose_due_unit": 9, "dose_given": 45, "dose_given_unit": 21, "will_remainder_of_dose_be_given": 2, "product_amount_given": 9, "product_unit": 1, "product_code": 0, "product_description": 31, "product_description_other": 2, "prior_infusion_rate": 10, "infusion_rate": 13, "infusion_rate_adjustment": 3, "infusion_rate_adjustment_amount": 0, "infusion_rate_unit": 8, "route": 0, "infusion_complete": 2, "completion_interval": 3, "new_iv_bag_hung": 1, "continued_infusion_in_other_location": 0, "restart_interval": 1, "side": 0, "site": 0, "non_formulary_visual_verification": 0}, "value_ranges": {"subject_id": {"min": 10002495.0, "max": 10040025.0, "mean": 10022754.646}, "emar_seq": {"min": 9.0, "max": 3691.0, "mean": 673.164}, "parent_field_ordinal": {"min": 1.1, "max": 1.1, "mean": 1.0999999999999999}, "pharmacy_id": {"min": 178524.0, "max": 99770953.0, "mean": 50574316.10837439}, "dose_due": {"min": null, "max": null, "mean": null}, "product_amount_given": {"min": 0.4, "max": 1000.0, "mean": 206.35675675675677}, "product_code": {"min": null, "max": null, "mean": null}, "prior_infusion_rate": {"min": 20.0, "max": 950.0, "mean": 125.58571428571427}, "infusion_rate": {"min": 0.5, "max": 950.0, "mean": 108.15882352941175}, "infusion_rate_adjustment_amount": {"min": null, "max": null, "mean": null}, "route": {"min": null, "max": null, "mean": null}, "continued_infusion_in_other_location": {"min": null, "max": null, "mean": null}, "side": {"min": null, "max": null, "mean": null}, "site": {"min": null, "max": null, "mean": null}, "non_formulary_visual_verification": {"min": null, "max": null, "mean": null}}}, "hcpcsevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\hcpcsevents.csv", "total_rows": 61, "columns": ["subject_id", "hadm_id", "chartdate", "hcpcs_cd", "seq_num", "short_description"], "column_count": 6, "data_types": {"subject_id": "int64", "hadm_id": "int64", "chartdate": "object", "hcpcs_cd": "object", "seq_num": "int64", "short_description": "object"}, "sample_data": {"subject_id": [10005348, 10005348, 10004457, 10004457, 10039708], "hadm_id": [29176490, 29176490, 21039249, 25559382, 27504040], "chartdate": ["2129-05-22", "2129-05-22", "2140-09-17", "2148-09-14", "2142-07-06"], "hcpcs_cd": ["93454", "92921", "92980", "93455", "64415"], "seq_num": [1, 2, 1, 1, 2], "short_description": ["Cardiovascular", "Cardiovascular", "Cardiovascular", "Cardiovascular", "Nervous system"]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "chartdate": 0, "hcpcs_cd": 0, "seq_num": 0, "short_description": 0}, "unique_counts": {"subject_id": 18, "hadm_id": 41, "chartdate": 42, "hcpcs_cd": 19, "seq_num": 4, "short_description": 11}, "value_ranges": {"subject_id": {"min": 10002428.0, "max": 10040025.0, "mean": 10017382.803278688}, "hadm_id": {"min": 20282368.0, "max": 29820177.0, "mean": 26017842.442622952}, "seq_num": {"min": 1.0, "max": 4.0, "mean": 1.****************}}}, "microbiologyevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\microbiologyevents.csv", "total_rows": 500, "columns": ["microevent_id", "subject_id", "hadm_id", "micro_specimen_id", "order_provider_id", "chartdate", "charttime", "spec_itemid", "spec_type_desc", "test_seq", "storedate", "storetime", "test_itemid", "test_name", "org_itemid", "org_name", "isolate_num", "quantity", "ab_itemid", "ab_name", "dilution_text", "dilution_comparison", "dilution_value", "interpretation", "comments"], "column_count": 25, "data_types": {"microevent_id": "int64", "subject_id": "int64", "hadm_id": "float64", "micro_specimen_id": "int64", "order_provider_id": "object", "chartdate": "object", "charttime": "object", "spec_itemid": "int64", "spec_type_desc": "object", "test_seq": "int64", "storedate": "object", "storetime": "object", "test_itemid": "int64", "test_name": "object", "org_itemid": "float64", "org_name": "object", "isolate_num": "float64", "quantity": "float64", "ab_itemid": "float64", "ab_name": "object", "dilution_text": "object", "dilution_comparison": "object", "dilution_value": "float64", "interpretation": "object", "comments": "object"}, "sample_data": {"microevent_id": [36, 15, 32, 7013, 12898], "subject_id": [10000032, 10000032, 10000032, 10020944, 10037975], "hadm_id": [25742920.0, 22595853.0, 29079034.0, 29974575.0, 27617929.0], "micro_specimen_id": [7814634, 5717063, 5901894, 4646730, 1636367], "order_provider_id": ["P79OSU", "P9156W", "P9156W", "P683OZ", "P62SH4"], "chartdate": ["2180-08-06 00:00:00", "2180-05-07 00:00:00", "2180-07-24 00:00:00", "2131-02-27 00:00:00", "2185-01-17 00:00:00"], "charttime": ["2180-08-06 20:35:00", "2180-05-07 00:19:00", "2180-07-24 00:55:00", "2131-02-27 17:41:00", "2185-01-17 21:32:00"], "spec_itemid": [70070, 70070, 70070, 70070, 70070], "spec_type_desc": ["SWAB", "SWAB", "SWAB", "SWAB", "SWAB"], "test_seq": [1, 1, 1, 1, 1], "storedate": ["2180-08-08 00:00:00", "2180-05-09 00:00:00", "2180-07-27 00:00:00", "2131-03-03 00:00:00", "2185-01-20 00:00:00"], "storetime": ["2180-08-08 08:03:00", "2180-05-09 07:57:00", "2180-07-27 11:15:00", "2131-03-03 13:09:00", "2185-01-20 07:56:00"], "test_itemid": [90115, 90115, 90115, 90115, 90115], "test_name": ["R/O VANCOMYCIN RESISTANT ENTEROCOCCUS", "R/O VANCOMYCIN RESISTANT ENTEROCOCCUS", "R/O VANCOMYCIN RESISTANT ENTEROCOCCUS", "R/O VANCOMYCIN RESISTANT ENTEROCOCCUS", "R/O VANCOMYCIN RESISTANT ENTEROCOCCUS"], "org_itemid": [80053.0, 80075.0, 80075.0, 80075.0, 80293.0], "org_name": ["ENTEROCOCCUS SP.", "YEAST", "YEAST", "YEAST", "POSITIVE FOR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> RESISTANT STAPH AUREUS"], "isolate_num": [1.0, 1.0, 1.0, 1.0, 1.0], "quantity": [], "ab_itemid": [90015.0, 90027.0, 90025.0, 90011.0, 90006.0], "ab_name": ["VANCOMYCIN", "RIFAMPIN", "LEVOFLOXACIN", "TETRACYCLINE", "ERYTHROMYCIN"], "dilution_text": [">256", "<=0.5", "=>8", "<=1", "=>4"], "dilution_comparison": ["<=        ", "=>        ", "<=        ", "=>        ", "=         "], "dilution_value": [0.5, 8.0, 1.0, 4.0, 1.0], "interpretation": ["R", "S", "R", "S", "R"], "comments": ["No VRE isolated.  ", "No VRE isolated.  ", "No VRE isolated.  ", "No VRE isolated.  ", "No VRE isolated.  "]}, "null_counts": {"microevent_id": 0, "subject_id": 0, "hadm_id": 188, "micro_specimen_id": 0, "order_provider_id": 377, "chartdate": 0, "charttime": 16, "spec_itemid": 0, "spec_type_desc": 0, "test_seq": 0, "storedate": 0, "storetime": 0, "test_itemid": 0, "test_name": 0, "org_itemid": 423, "org_name": 423, "isolate_num": 423, "quantity": 500, "ab_itemid": 438, "ab_name": 438, "dilution_text": 442, "dilution_comparison": 443, "dilution_value": 443, "interpretation": 438, "comments": 68}, "unique_counts": {"microevent_id": 500, "subject_id": 91, "hadm_id": 122, "micro_specimen_id": 413, "order_provider_id": 22, "chartdate": 284, "charttime": 360, "spec_itemid": 20, "spec_type_desc": 17, "test_seq": 8, "storedate": 338, "storetime": 399, "test_itemid": 28, "test_name": 28, "org_itemid": 11, "org_name": 11, "isolate_num": 1, "quantity": 0, "ab_itemid": 20, "ab_name": 20, "dilution_text": 13, "dilution_comparison": 3, "dilution_value": 8, "interpretation": 2, "comments": 35}, "value_ranges": {"microevent_id": {"min": 1.0, "max": 14529.0, "mean": 7187.328}, "subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10020831.122}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25295564.865384616}, "micro_specimen_id": {"min": 9430.0, "max": 9980461.0, "mean": 5311699.366}, "spec_itemid": {"min": 70012.0, "max": 70093.0, "mean": 70048.184}, "test_seq": {"min": 1.0, "max": 9.0, "mean": 1.216}, "test_itemid": {"min": 90115.0, "max": 90201.0, "mean": 90183.672}, "org_itemid": {"min": 80002.0, "max": 90760.0, "mean": 80373.**********}, "isolate_num": {"min": 1.0, "max": 1.0, "mean": 1.0}, "quantity": {"min": null, "max": null, "mean": null}, "ab_itemid": {"min": 90003.0, "max": 90029.0, "mean": 90012.70967741935}, "dilution_value": {"min": 0.06, "max": 8.0, "mean": 1.1805263157894739}}}, "omr": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\omr.csv", "total_rows": 500, "columns": ["subject_id", "chartdate", "seq_num", "result_name", "result_value"], "column_count": 5, "data_types": {"subject_id": "int64", "chartdate": "object", "seq_num": "int64", "result_name": "object", "result_value": "object"}, "sample_data": {"subject_id": [10011398, 10011398, 10011398, 10011398, 10011398], "chartdate": ["2146-12-01", "2147-01-22", "2146-12-01", "2147-07-24", "2147-03-26"], "seq_num": [1, 1, 1, 1, 1], "result_name": ["Height (Inches)", "Weight (Lbs)", "Weight (Lbs)", "Weight (Lbs)", "Weight (Lbs)"], "result_value": ["63", "127", "135", "136", "136"]}, "null_counts": {"subject_id": 0, "chartdate": 0, "seq_num": 0, "result_name": 0, "result_value": 0}, "unique_counts": {"subject_id": 21, "chartdate": 167, "seq_num": 18, "result_name": 4, "result_value": 321}, "value_ranges": {"subject_id": {"min": 10001217.0, "max": 10038081.0, "mean": 10010726.624}, "seq_num": {"min": 1.0, "max": 18.0, "mean": 1.668}}}, "pharmacy": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\pharmacy.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "pharmacy_id", "poe_id", "starttime", "stoptime", "medication", "proc_type", "status", "entertime", "verifiedtime", "route", "frequency", "disp_sched", "infusion_type", "sliding_scale", "lockout_interval", "basal_rate", "one_hr_max", "doses_per_24_hrs", "duration", "duration_interval", "expiration_value", "expiration_unit", "expirationdate", "dispensation", "fill_quantity"], "column_count": 27, "data_types": {"subject_id": "int64", "hadm_id": "int64", "pharmacy_id": "int64", "poe_id": "object", "starttime": "object", "stoptime": "object", "medication": "object", "proc_type": "object", "status": "object", "entertime": "object", "verifiedtime": "object", "route": "object", "frequency": "object", "disp_sched": "object", "infusion_type": "float64", "sliding_scale": "float64", "lockout_interval": "float64", "basal_rate": "float64", "one_hr_max": "float64", "doses_per_24_hrs": "float64", "duration": "float64", "duration_interval": "object", "expiration_value": "float64", "expiration_unit": "object", "expirationdate": "float64", "dispensation": "object", "fill_quantity": "float64"}, "sample_data": {"subject_id": [10027602, 10027602, 10027602, 10027602, 10027602], "hadm_id": [28166872, 28166872, 28166872, 28166872, 28166872], "pharmacy_id": [24340150, 14435820, 40720238, 27168639, 62845687], "poe_id": ["10020740-96", "10002428-187", "10002428-219", "10002428-235", "10002428-356"], "starttime": ["2201-10-30 12:00:00", "2201-10-30 12:00:00", "2201-10-30 12:00:00", "2201-10-30 12:00:00", "2201-10-31 12:00:00"], "stoptime": ["2156-04-17 16:00:00", "2156-04-20 08:00:00", "2156-04-23 10:00:00", "2136-04-22 18:00:00", "2136-04-23 19:00:00"], "medication": ["Midazolam", "Midazolam", "Fentanyl Citrate", "Fentanyl Citrate", "Lorazepam"], "proc_type": ["Miscellaneous Charges", "Miscellaneous Charges", "Miscellaneous Charges", "Miscellaneous Charges", "Miscellaneous Charges"], "status": ["Inactive (Due to a change order)", "Inactive (Due to a change order)", "Inactive (Due to a change order)", "Inactive (Due to a change order)", "Inactive (Due to a change order)"], "entertime": ["2201-10-30 12:32:11", "2201-10-30 12:54:34", "2201-10-30 12:32:11", "2201-10-30 12:54:34", "2201-10-31 12:02:42"], "verifiedtime": ["2150-03-12 14:42:34", "2156-04-17 09:32:56", "2156-04-18 11:03:53", "2156-04-19 12:44:20", "2156-04-20 12:06:57"], "route": ["IV", "IV DRIP", "TP", "TP", "TP"], "frequency": ["1X", "1X", "1X", "1X", "1X"], "disp_sched": ["21", "13", "0", "3", "4"], "infusion_type": [], "sliding_scale": [], "lockout_interval": [], "basal_rate": [], "one_hr_max": [], "doses_per_24_hrs": [0.0, 0.0, 0.0, 0.0, 1.0], "duration": [1.0, 1.0, 1.0, 1.0, 1.0], "duration_interval": ["Do<PERSON>", "Do<PERSON>", "Do<PERSON>", "Do<PERSON>", "Do<PERSON>"], "expiration_value": [365.0, 36.0, 36.0, 36.0, 36.0], "expiration_unit": ["Enter on Label", "Enter on Label", "Days", "Hours", "Hours"], "expirationdate": [], "dispensation": ["Omnicell", "LET CALL", "Omnicell", "Omnicell", "Omnicell"], "fill_quantity": []}, "null_counts": {"subject_id": 0, "hadm_id": 0, "pharmacy_id": 0, "poe_id": 12, "starttime": 0, "stoptime": 73, "medication": 85, "proc_type": 0, "status": 0, "entertime": 0, "verifiedtime": 6, "route": 87, "frequency": 87, "disp_sched": 93, "infusion_type": 500, "sliding_scale": 500, "lockout_interval": 500, "basal_rate": 500, "one_hr_max": 500, "doses_per_24_hrs": 129, "duration": 441, "duration_interval": 87, "expiration_value": 98, "expiration_unit": 87, "expirationdate": 500, "dispensation": 87, "fill_quantity": 500}, "unique_counts": {"subject_id": 78, "hadm_id": 142, "pharmacy_id": 500, "poe_id": 470, "starttime": 394, "stoptime": 307, "medication": 72, "proc_type": 4, "status": 4, "entertime": 432, "verifiedtime": 428, "route": 13, "frequency": 5, "disp_sched": 23, "infusion_type": 0, "sliding_scale": 0, "lockout_interval": 0, "basal_rate": 0, "one_hr_max": 0, "doses_per_24_hrs": 3, "duration": 5, "duration_interval": 3, "expiration_value": 2, "expiration_unit": 3, "expirationdate": 0, "dispensation": 7, "fill_quantity": 0}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018958.0}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25412700.462}, "pharmacy_id": {"min": 213266.0, "max": 99960088.0, "mean": 49224768.74}, "infusion_type": {"min": null, "max": null, "mean": null}, "sliding_scale": {"min": null, "max": null, "mean": null}, "lockout_interval": {"min": null, "max": null, "mean": null}, "basal_rate": {"min": null, "max": null, "mean": null}, "one_hr_max": {"min": null, "max": null, "mean": null}, "doses_per_24_hrs": {"min": 0.0, "max": 2.0, "mean": 1.4231805929919137}, "duration": {"min": 1.0, "max": 7.0, "mean": 1.7966101694915255}, "expiration_value": {"min": 36.0, "max": 365.0, "mean": 162.03482587064676}, "expirationdate": {"min": null, "max": null, "mean": null}, "fill_quantity": {"min": null, "max": null, "mean": null}}}, "poe": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\poe.csv", "total_rows": 500, "columns": ["poe_id", "poe_seq", "subject_id", "hadm_id", "ordertime", "order_type", "order_subtype", "transaction_type", "discontinue_of_poe_id", "discontinued_by_poe_id", "order_provider_id", "order_status"], "column_count": 12, "data_types": {"poe_id": "object", "poe_seq": "int64", "subject_id": "int64", "hadm_id": "int64", "ordertime": "object", "order_type": "object", "order_subtype": "object", "transaction_type": "object", "discontinue_of_poe_id": "object", "discontinued_by_poe_id": "object", "order_provider_id": "object", "order_status": "object"}, "sample_data": {"poe_id": ["10002930-456", "10002930-454", "10002930-455", "10002930-453", "10002930-452"], "poe_seq": [456, 454, 455, 453, 452], "subject_id": [10002930, 10002930, 10002930, 10002930, 10002930], "hadm_id": [20282368, 20282368, 20282368, 20282368, 20282368], "ordertime": ["2201-03-23 19:14:33", "2201-03-23 19:14:33", "2201-03-23 19:14:33", "2201-03-23 19:14:33", "2201-03-23 19:14:33"], "order_type": ["General Care", "General Care", "General Care", "IV therapy", "ADT orders"], "order_subtype": ["Other", "Vitals/Monitoring", "Activity", "IV access", "Admit"], "transaction_type": ["New", "New", "New", "New", "New"], "discontinue_of_poe_id": ["10002930-443", "10002930-458", "10002930-473", "10002930-445", "10002930-479"], "discontinued_by_poe_id": ["10002930-457", "10002930-460", "10002930-458", "10002930-475", "10002930-465"], "order_provider_id": ["P04TDP", "P04TDP", "P04TDP", "P04TDP", "P04TDP"], "order_status": ["Inactive", "Inactive", "Inactive", "Inactive", "Inactive"]}, "null_counts": {"poe_id": 0, "poe_seq": 0, "subject_id": 0, "hadm_id": 0, "ordertime": 0, "order_type": 0, "order_subtype": 337, "transaction_type": 0, "discontinue_of_poe_id": 360, "discontinued_by_poe_id": 371, "order_provider_id": 23, "order_status": 0}, "unique_counts": {"poe_id": 500, "poe_seq": 366, "subject_id": 4, "hadm_id": 4, "ordertime": 280, "order_type": 11, "order_subtype": 38, "transaction_type": 3, "discontinue_of_poe_id": 139, "discontinued_by_poe_id": 129, "order_provider_id": 42, "order_status": 1}, "value_ranges": {"poe_seq": {"min": 20.0, "max": 484.0, "mean": 255.884}, "subject_id": {"min": 10002930.0, "max": 10038081.0, "mean": 10027814.124}, "hadm_id": {"min": 20282368.0, "max": 26275841.0, "mean": 23237527.136}}}, "poe_detail": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\poe_detail.csv", "total_rows": 500, "columns": ["poe_id", "poe_seq", "subject_id", "field_name", "field_value"], "column_count": 5, "data_types": {"poe_id": "object", "poe_seq": "int64", "subject_id": "int64", "field_name": "object", "field_value": "object"}, "sample_data": {"poe_id": ["10011398-23", "10011398-103", "10011398-163", "10011398-109", "10011398-35"], "poe_seq": [23, 103, 163, 109, 35], "subject_id": [10011398, 10011398, 10011398, 10011398, 10011398], "field_name": ["Admit to", "Transfer to", "Discharge Planning", "Tubes & Drains type", "Tubes & Drains type"], "field_value": ["Surgery", "Surgery", "Finalized", "Chest tube", "Chest tube"]}, "null_counts": {"poe_id": 0, "poe_seq": 0, "subject_id": 0, "field_name": 0, "field_value": 0}, "unique_counts": {"poe_id": 363, "poe_seq": 299, "subject_id": 21, "field_name": 8, "field_value": 19}, "value_ranges": {"poe_seq": {"min": 5.0, "max": 1519.0, "mean": 406.216}, "subject_id": {"min": 10004235.0, "max": 10039831.0, "mean": 10015791.254}}}, "provider": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\provider.csv", "total_rows": 500, "columns": ["provider_id"], "column_count": 1, "data_types": {"provider_id": "object"}, "sample_data": {"provider_id": ["P003F3", "P005JG", "P005MB", "P00707", "P009IB"]}, "null_counts": {"provider_id": 0}, "unique_counts": {"provider_id": 500}, "value_ranges": {}}, "services": {"file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\services.csv", "total_rows": 319, "columns": ["subject_id", "hadm_id", "transfertime", "prev_service", "curr_service"], "column_count": 5, "data_types": {"subject_id": "int64", "hadm_id": "int64", "transfertime": "object", "prev_service": "object", "curr_service": "object"}, "sample_data": {"subject_id": [10001725, 10019003, 10007818, 10004235, 10026255], "hadm_id": [25563031, 28003918, 22987108, 24181354, 22059910], "transfertime": ["2110-04-11 15:09:36", "2148-12-21 03:32:53", "2146-06-10 16:38:18", "2196-02-24 14:39:31", "2201-07-07 18:16:14"], "prev_service": ["TRAUM", "SURG", "NSURG", "OMED", "SURG"], "curr_service": ["GYN", "GYN", "MED", "MED", "MED"]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "transfertime": 0, "prev_service": 275, "curr_service": 0}, "unique_counts": {"subject_id": 100, "hadm_id": 275, "transfertime": 319, "prev_service": 10, "curr_service": 13}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018591.363636363}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25198664.755485892}}}, "caregiver": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\caregiver.csv", "total_rows": 500, "columns": ["caregiver_id"], "column_count": 1, "data_types": {"caregiver_id": "int64"}, "sample_data": {"caregiver_id": [444, 1016, 1135, 1172, 1353]}, "null_counts": {"caregiver_id": 0}, "unique_counts": {"caregiver_id": 500}, "value_ranges": {"caregiver_id": {"min": 10.0, "max": 99730.0, "mean": 43184.94}}}, "datetimeevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\datetimeevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "charttime", "storetime", "itemid", "value", "valueuom", "warning"], "column_count": 10, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "int64", "charttime": "object", "storetime": "object", "itemid": "int64", "value": "object", "valueuom": "object", "warning": "int64"}, "sample_data": {"subject_id": [10002428, 10002428, 10002428, 10002428, 10002428], "hadm_id": [23473524, 23473524, 23473524, 23473524, 23473524], "stay_id": [35479615, 35479615, 35479615, 35479615, 35479615], "caregiver_id": [29441, 29441, 29441, 29441, 29441], "charttime": ["2156-05-15 10:50:00", "2156-05-15 10:50:00", "2156-05-15 10:50:00", "2156-05-15 09:00:00", "2156-05-15 09:00:00"], "storetime": ["2156-05-15 10:50:00", "2156-05-15 10:50:00", "2156-05-15 10:50:00", "2156-05-15 10:51:00", "2156-05-15 10:51:00"], "itemid": [225343, 225348, 225345, 224186, 224187], "value": ["2156-05-11 00:00:00", "2156-05-11 00:00:00", "2156-05-14 09:00:00", "2156-05-15 09:00:00", "2156-05-15 10:50:00"], "valueuom": ["Date", "Date", "Date and Time", "Date and Time", "Date"], "warning": [0, 0, 0, 0, 0]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 0, "charttime": 0, "storetime": 0, "itemid": 0, "value": 0, "valueuom": 0, "warning": 0}, "unique_counts": {"subject_id": 13, "hadm_id": 15, "stay_id": 17, "caregiver_id": 8, "charttime": 117, "storetime": 122, "itemid": 36, "value": 90, "valueuom": 2, "warning": 1}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10038999.0, "mean": 10018619.85}, "hadm_id": {"min": 21607814.0, "max": 29600294.0, "mean": 26170381.27}, "stay_id": {"min": 30057454.0, "max": 39864867.0, "mean": 33439435.336}, "caregiver_id": {"min": 3849.0, "max": 78599.0, "mean": 42472.006}, "itemid": {"min": 224183.0, "max": 229352.0, "mean": 224872.34}, "warning": {"min": 0.0, "max": 0.0, "mean": 0.0}}}, "d_items": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\d_items.csv", "total_rows": 500, "columns": ["itemid", "label", "abbreviation", "linksto", "category", "unitname", "param_type", "lownormalvalue", "highnormalvalue"], "column_count": 9, "data_types": {"itemid": "int64", "label": "object", "abbreviation": "object", "linksto": "object", "category": "object", "unitname": "float64", "param_type": "object", "lownormalvalue": "float64", "highnormalvalue": "float64"}, "sample_data": {"itemid": [226228, 226545, 229877, 229875, 229266], "label": ["Gender", "Race", "Suction events (CH)", "Oxygenator visible (CH)", "Cannula sites visually inspected (ECMO)"], "abbreviation": ["Gender", "Race", "Suction events (CH)", "Oxygenator visible (CH)", "Cannula sites visually inspected (ECMO)"], "linksto": ["chartevents", "chartevents", "chartevents", "chartevents", "chartevents"], "category": ["ADT", "ADT", "ECMO", "ECMO", "ECMO"], "unitname": [], "param_type": ["Text", "Text", "Text", "Text", "Text"], "lownormalvalue": [], "highnormalvalue": []}, "null_counts": {"itemid": 0, "label": 0, "abbreviation": 0, "linksto": 0, "category": 0, "unitname": 500, "param_type": 0, "lownormalvalue": 500, "highnormalvalue": 500}, "unique_counts": {"itemid": 500, "label": 479, "abbreviation": 476, "linksto": 2, "category": 21, "unitname": 0, "param_type": 1, "lownormalvalue": 0, "highnormalvalue": 0}, "value_ranges": {"itemid": {"min": 220001.0, "max": 230085.0, "mean": 227273.426}, "unitname": {"min": null, "max": null, "mean": null}, "lownormalvalue": {"min": null, "max": null, "mean": null}, "highnormalvalue": {"min": null, "max": null, "mean": null}}}, "icustays": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\icustays.csv", "total_rows": 140, "columns": ["subject_id", "hadm_id", "stay_id", "first_careunit", "last_careunit", "intime", "outtime", "los"], "column_count": 8, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "first_careunit": "object", "last_careunit": "object", "intime": "object", "outtime": "object", "los": "float64"}, "sample_data": {"subject_id": [10018328, 10020187, 10020187, 10012853, 10020740], "hadm_id": [23786647, 24104168, 26842957, 27882036, 25826145], "stay_id": [31269608, 37509585, 32554129, 31338022, 32145159], "first_careunit": ["Neuro Stepdown", "Neuro Surgical Intensive Care Unit (Neuro SICU)", "Neuro Intermediate", "Trauma SICU (TSICU)", "Trauma SICU (TSICU)"], "last_careunit": ["Neuro Stepdown", "Neuro Stepdown", "Neuro Intermediate", "Trauma SICU (TSICU)", "Trauma SICU (TSICU)"], "intime": ["2154-04-24 23:03:44", "2169-01-15 04:56:00", "2170-02-24 18:18:46", "2176-11-26 02:34:49", "2150-06-03 20:12:32"], "outtime": ["2154-05-02 15:55:21", "2169-01-20 15:47:50", "2170-02-25 15:15:26", "2176-11-29 20:58:54", "2150-06-04 21:05:58"], "los": [7.702511574074075, 5.452662037037037, 0.8726851851851852, 3.766724537037037, 1.0371064814814817]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "first_careunit": 0, "last_careunit": 0, "intime": 0, "outtime": 0, "los": 0}, "unique_counts": {"subject_id": 100, "hadm_id": 128, "stay_id": 140, "first_careunit": 9, "last_careunit": 9, "intime": 140, "outtime": 140, "los": 140}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018563.32142857}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25150128.464285713}, "stay_id": {"min": 30057454.0, "max": 39880770.0, "mean": 35102545.20714286}, "los": {"min": 0.0237268518518518, "max": 20.52868055555556, "mean": 3.6793790509259257}}}, "ingredientevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\ingredientevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "starttime", "endtime", "storetime", "itemid", "amount", "amountuom", "rate", "rateuom", "orderid", "linkorderid", "statusdescription", "originalamount", "originalrate"], "column_count": 17, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "int64", "starttime": "object", "endtime": "object", "storetime": "object", "itemid": "int64", "amount": "float64", "amountuom": "object", "rate": "float64", "rateuom": "object", "orderid": "int64", "linkorderid": "int64", "statusdescription": "object", "originalamount": "int64", "originalrate": "float64"}, "sample_data": {"subject_id": [10005817, 10005817, 10005817, 10005817, 10005817], "hadm_id": [20626031, 20626031, 20626031, 20626031, 20626031], "stay_id": [32604416, 32604416, 32604416, 32604416, 32604416], "caregiver_id": [4793, 4793, 20310, 20310, 92805], "starttime": ["2132-12-17 05:00:00", "2132-12-17 05:00:00", "2132-12-17 12:00:00", "2132-12-17 12:00:00", "2132-12-15 16:35:00"], "endtime": ["2132-12-17 06:00:00", "2132-12-17 06:00:00", "2132-12-17 13:00:00", "2132-12-17 13:00:00", "2132-12-15 18:00:00"], "storetime": ["2132-12-17 06:01:00", "2132-12-17 06:01:00", "2132-12-17 12:48:00", "2132-12-17 12:48:00", "2132-12-15 16:42:00"], "itemid": [227074, 220490, 220490, 226509, 220490], "amount": [49.999998807907104, 49.999998807907104, 249.99999046325684, 249.99999046325684, 38.852669447660446], "amountuom": ["ml", "ml", "ml", "ml", "ml"], "rate": [50.0, 50.0, 249.9999847412109, 249.9999847412109, 27.425413131713867], "rateuom": ["mL/hour", "mL/hour", "mL/hour", "mL/hour", "mL/hour"], "orderid": [7330951, 7330951, 5334154, 5334154, 1386365], "linkorderid": [7330951, 7330951, 5334154, 5334154, 3042892], "statusdescription": ["FinishedRunning", "FinishedRunning", "FinishedRunning", "FinishedRunning", "ChangeDose/Rate"], "originalamount": [0, 0, 0, 0, 0], "originalrate": [50.0, 50.0, 250.0, 250.0, 47.08029174804688]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 0, "starttime": 0, "endtime": 0, "storetime": 0, "itemid": 0, "amount": 0, "amountuom": 0, "rate": 147, "rateuom": 147, "orderid": 0, "linkorderid": 0, "statusdescription": 0, "originalamount": 0, "originalrate": 0}, "unique_counts": {"subject_id": 5, "hadm_id": 5, "stay_id": 5, "caregiver_id": 23, "starttime": 169, "endtime": 178, "storetime": 151, "itemid": 11, "amount": 192, "amountuom": 4, "rate": 146, "rateuom": 2, "orderid": 204, "linkorderid": 150, "statusdescription": 4, "originalamount": 1, "originalrate": 143}, "value_ranges": {"subject_id": {"min": 10005348.0, "max": 10027445.0, "mean": 10008396.384}, "hadm_id": {"min": 20626031.0, "max": 29163082.0, "mean": 23449832.978}, "stay_id": {"min": 32145159.0, "max": 36084484.0, "mean": 33784125.712}, "caregiver_id": {"min": 4793.0, "max": 92805.0, "mean": 50248.046}, "itemid": {"min": 220363.0, "max": 227075.0, "mean": 224028.704}, "amount": {"min": 0.0180000006366753, "max": 25000.001907348636, "mean": 285.67617388906683}, "rate": {"min": 0.5867471694946289, "max": 8810.06908416748, "mean": 238.793950248879}, "orderid": {"min": 14306.0, "max": 9906593.0, "mean": 4946721.788}, "linkorderid": {"min": 14306.0, "max": 9906593.0, "mean": 5275280.63}, "originalamount": {"min": 0.0, "max": 0.0, "mean": 0.0}, "originalrate": {"min": 0.0179999992251396, "max": 25000.0, "mean": 337.31135198269783}}}, "inputevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\inputevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "starttime", "endtime", "storetime", "itemid", "amount", "amountuom", "rate", "rateuom", "orderid", "linkorderid", "ordercategoryname", "secondaryordercategoryname", "ordercomponenttypedescription", "ordercategorydescription", "patientweight", "totalamount", "totalamountuom", "isopenbag", "continueinnextdept", "statusdescription", "originalamount", "originalrate"], "column_count": 26, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "int64", "starttime": "object", "endtime": "object", "storetime": "object", "itemid": "int64", "amount": "float64", "amountuom": "object", "rate": "float64", "rateuom": "object", "orderid": "int64", "linkorderid": "int64", "ordercategoryname": "object", "secondaryordercategoryname": "object", "ordercomponenttypedescription": "object", "ordercategorydescription": "object", "patientweight": "float64", "totalamount": "float64", "totalamountuom": "object", "isopenbag": "int64", "continueinnextdept": "int64", "statusdescription": "object", "originalamount": "float64", "originalrate": "float64"}, "sample_data": {"subject_id": [10005817, 10005817, 10005817, 10005817, 10005817], "hadm_id": [20626031, 20626031, 20626031, 20626031, 20626031], "stay_id": [32604416, 32604416, 32604416, 32604416, 32604416], "caregiver_id": [4793, 92805, 20310, 79166, 92805], "starttime": ["2132-12-16 19:50:00", "2132-12-15 20:15:00", "2132-12-17 09:15:00", "2132-12-16 09:36:00", "2132-12-15 20:10:00"], "endtime": ["2132-12-16 19:51:00", "2132-12-15 20:16:00", "2132-12-17 09:16:00", "2132-12-16 09:37:00", "2132-12-15 21:10:00"], "storetime": ["2132-12-16 19:50:00", "2132-12-15 20:11:00", "2132-12-17 09:28:00", "2132-12-16 09:37:00", "2132-12-15 20:10:00"], "itemid": [225798, 225798, 225798, 225798, 221456], "amount": [1.0, 1.0, 1.0, 1.0, 2.0000001043081284], "amountuom": ["dose", "dose", "dose", "dose", "grams"], "rate": [5.003638744354248, 40.90279769897461, 20.48247718811035, 10.0, 27.23836898803711], "rateuom": ["mL/hour", "mL/hour", "mL/hour", "mL/hour", "mL/hour"], "orderid": [3316866, 8515923, 8912103, 4059842, 6323189], "linkorderid": [3316866, 8515923, 8912103, 4059842, 6323189], "ordercategoryname": ["08-Antibiotics (IV)", "08-Antibiotics (IV)", "08-Antibiotics (IV)", "08-Antibiotics (IV)", "02-Fluids (Crystalloids)"], "secondaryordercategoryname": ["02-Fluids (Crystalloids)", "02-Fluids (Crystalloids)", "02-Fluids (Crystalloids)", "02-Fluids (Crystalloids)", "Additive (Crystalloid)"], "ordercomponenttypedescription": ["Main order parameter", "Main order parameter", "Main order parameter", "Main order parameter", "Additives                                         Ampoule                                           "], "ordercategorydescription": ["Drug Push", "Drug Push", "Drug Push", "Drug Push", "Continuous IV"], "patientweight": [91.0, 91.0, 91.0, 91.0, 91.0], "totalamount": [500.0, 500.0, 500.0, 500.0, 100.0], "totalamountuom": ["ml", "ml", "ml", "ml", "ml"], "isopenbag": [0, 0, 0, 0, 0], "continueinnextdept": [0, 0, 0, 0, 0], "statusdescription": ["FinishedRunning", "FinishedRunning", "FinishedRunning", "FinishedRunning", "FinishedRunning"], "originalamount": [1.0, 1.0, 1.0, 1.0, 2.0], "originalrate": [1.0, 1.0, 1.0, 1.0, 0.0333333350718021]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 0, "starttime": 0, "endtime": 0, "storetime": 0, "itemid": 0, "amount": 0, "amountuom": 0, "rate": 189, "rateuom": 189, "orderid": 0, "linkorderid": 0, "ordercategoryname": 0, "secondaryordercategoryname": 146, "ordercomponenttypedescription": 0, "ordercategorydescription": 0, "patientweight": 0, "totalamount": 82, "totalamountuom": 82, "isopenbag": 0, "continueinnextdept": 0, "statusdescription": 0, "originalamount": 0, "originalrate": 0}, "unique_counts": {"subject_id": 6, "hadm_id": 6, "stay_id": 6, "caregiver_id": 26, "starttime": 271, "endtime": 295, "storetime": 238, "itemid": 56, "amount": 289, "amountuom": 7, "rate": 216, "rateuom": 5, "orderid": 339, "linkorderid": 260, "ordercategoryname": 12, "secondaryordercategoryname": 2, "ordercomponenttypedescription": 3, "ordercategorydescription": 5, "patientweight": 6, "totalamount": 28, "totalamountuom": 1, "isopenbag": 1, "continueinnextdept": 1, "statusdescription": 4, "originalamount": 207, "originalrate": 169}, "value_ranges": {"subject_id": {"min": 10005348.0, "max": 10027445.0, "mean": 10008006.442}, "hadm_id": {"min": 20626031.0, "max": 29163082.0, "mean": 23251390.32}, "stay_id": {"min": 32145159.0, "max": 36084484.0, "mean": 33815169.498}, "caregiver_id": {"min": 4793.0, "max": 92805.0, "mean": 51210.176}, "itemid": {"min": 220862.0, "max": 229071.0, "mean": 223640.918}, "amount": {"min": 0.0455166155006736, "max": 4000.0, "mean": 132.21863373255974}, "rate": {"min": 0.0993665453279391, "max": 2800.0, "mean": 82.22627271400013}, "orderid": {"min": 14306.0, "max": 9985422.0, "mean": 4978437.92}, "linkorderid": {"min": 14306.0, "max": 9985422.0, "mean": 5196201.234}, "patientweight": {"min": 69.0, "max": 103.0, "mean": 87.5112}, "totalamount": {"min": 50.0, "max": 4000.0, "mean": 234.51674641148324}, "isopenbag": {"min": 0.0, "max": 0.0, "mean": 0.0}, "continueinnextdept": {"min": 0.0, "max": 0.0, "mean": 0.0}, "originalamount": {"min": 0.0469999983906745, "max": 4000.0, "mean": 181.20396638263762}, "originalrate": {"min": 0.0, "max": 4000.0, "mean": 106.52194086476415}}}, "outputevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\outputevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "charttime", "storetime", "itemid", "value", "valueuom"], "column_count": 9, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "int64", "charttime": "object", "storetime": "object", "itemid": "int64", "value": "int64", "valueuom": "object"}, "sample_data": {"subject_id": [10002428, 10002428, 10002428, 10002428, 10002428], "hadm_id": [23473524, 23473524, 23473524, 23473524, 23473524], "stay_id": [35479615, 35479615, 35479615, 35479615, 35479615], "caregiver_id": [29441, 29441, 29441, 29441, 29441], "charttime": ["2156-05-15 18:00:00", "2156-05-15 12:00:00", "2156-05-15 13:00:00", "2156-05-15 08:00:00", "2156-05-15 14:00:00"], "storetime": ["2156-05-15 17:42:00", "2156-05-15 12:08:00", "2156-05-15 13:00:00", "2156-05-15 08:39:00", "2156-05-15 13:56:00"], "itemid": [226583, 226559, 226559, 226559, 226559], "value": [600, 60, 45, 125, 60], "valueuom": ["ml", "ml", "ml", "ml", "ml"]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 0, "charttime": 0, "storetime": 0, "itemid": 0, "value": 0, "valueuom": 0}, "unique_counts": {"subject_id": 27, "hadm_id": 32, "stay_id": 35, "caregiver_id": 21, "charttime": 445, "storetime": 390, "itemid": 24, "value": 79, "valueuom": 1}, "value_ranges": {"subject_id": {"min": 10002428.0, "max": 10038999.0, "mean": 10015687.894}, "hadm_id": {"min": 20214994.0, "max": 29276678.0, "mean": 24353759.732}, "stay_id": {"min": 30057454.0, "max": 39711498.0, "mean": 34037254.0}, "caregiver_id": {"min": 2826.0, "max": 99850.0, "mean": 40385.078}, "itemid": {"min": 226559.0, "max": 227510.0, "mean": 226607.004}, "value": {"min": 0.0, "max": 3675.0, "mean": 135.568}}}, "procedureevents": {"file_path": "mimic-iv-clinical-database-demo-2.2\\icu\\procedureevents.csv", "total_rows": 500, "columns": ["subject_id", "hadm_id", "stay_id", "caregiver_id", "starttime", "endtime", "storetime", "itemid", "value", "valueuom", "location", "locationcategory", "orderid", "linkorderid", "ordercategoryname", "ordercategorydescription", "patientweight", "isopenbag", "continueinnextdept", "statusdescription", "ORIGINALAMOUNT", "ORIGINALRATE"], "column_count": 22, "data_types": {"subject_id": "int64", "hadm_id": "int64", "stay_id": "int64", "caregiver_id": "float64", "starttime": "object", "endtime": "object", "storetime": "object", "itemid": "int64", "value": "float64", "valueuom": "object", "location": "object", "locationcategory": "object", "orderid": "int64", "linkorderid": "int64", "ordercategoryname": "object", "ordercategorydescription": "object", "patientweight": "float64", "isopenbag": "int64", "continueinnextdept": "int64", "statusdescription": "object", "ORIGINALAMOUNT": "float64", "ORIGINALRATE": "int64"}, "sample_data": {"subject_id": [10027445, 10027445, 10027445, 10027445, 10027445], "hadm_id": [26275841, 26275841, 26275841, 26275841, 26275841], "stay_id": [34499716, 34499716, 34499716, 34499716, 34499716], "caregiver_id": [10712.0, 80518.0, 96407.0, 96407.0, 80518.0], "starttime": ["2142-07-31 01:54:00", "2142-07-31 08:18:00", "2142-07-31 06:00:00", "2142-07-31 02:00:00", "2142-08-03 08:00:00"], "endtime": ["2142-08-02 10:44:00", "2142-08-03 15:10:00", "2142-08-03 06:03:00", "2142-08-03 05:57:00", "2142-08-03 21:06:00"], "storetime": ["2142-08-02 10:44:00", "2142-08-03 15:23:00", "2142-08-03 08:16:00", "2142-08-03 08:15:00", "2142-08-03 21:06:00.090"], "itemid": [225792, 224263, 224275, 224275, 224277], "value": [3410.0, 4732.0, 4323.0, 4557.0, 786.0], "valueuom": ["min", "min", "min", "min", "min"], "location": ["Right Antecubital", "Left Basilic Lower Arm", "Left Cephalic Lower Arm", "Left Radial", "Left Cephalic Lower Arm"], "locationcategory": ["Per<PERSON>heral", "Per<PERSON>heral", "Per<PERSON>heral", "Invasive Arterial", "Per<PERSON>heral"], "orderid": [532221, 401769, 9714245, 2870557, 4920092], "linkorderid": [532221, 401769, 9714245, 2870557, 4920092], "ordercategoryname": ["Ventilation", "Invasive Lines", "Peripheral Lines", "Peripheral Lines", "Peripheral Lines"], "ordercategorydescription": ["ContinuousProcess", "ContinuousProcess", "ContinuousProcess", "ContinuousProcess", "ContinuousProcess"], "patientweight": [103.0, 103.0, 103.0, 103.0, 103.0], "isopenbag": [1, 1, 1, 1, 1], "continueinnextdept": [0, 0, 0, 0, 0], "statusdescription": ["FinishedRunning", "FinishedRunning", "FinishedRunning", "FinishedRunning", "FinishedRunning"], "ORIGINALAMOUNT": [3410.0, 4732.0, 4323.0, 4557.0, 786.0], "ORIGINALRATE": [1, 1, 1, 1, 1]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "stay_id": 0, "caregiver_id": 81, "starttime": 0, "endtime": 0, "storetime": 0, "itemid": 0, "value": 0, "valueuom": 258, "location": 377, "locationcategory": 377, "orderid": 0, "linkorderid": 0, "ordercategoryname": 0, "ordercategorydescription": 0, "patientweight": 0, "isopenbag": 0, "continueinnextdept": 0, "statusdescription": 0, "ORIGINALAMOUNT": 0, "ORIGINALRATE": 0}, "unique_counts": {"subject_id": 41, "hadm_id": 45, "stay_id": 45, "caregiver_id": 124, "starttime": 414, "endtime": 444, "storetime": 397, "itemid": 59, "value": 224, "valueuom": 3, "location": 26, "locationcategory": 5, "orderid": 500, "linkorderid": 499, "ordercategoryname": 12, "ordercategorydescription": 2, "patientweight": 44, "isopenbag": 2, "continueinnextdept": 1, "statusdescription": 3, "ORIGINALAMOUNT": 215, "ORIGINALRATE": 2}, "value_ranges": {"subject_id": {"min": 10001217.0, "max": 10039997.0, "mean": 10020022.366}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25687894.612}, "stay_id": {"min": 30057454.0, "max": 39880770.0, "mean": 34913052.754}, "caregiver_id": {"min": 199.0, "max": 99850.0, "mean": 51371.77326968974}, "itemid": {"min": 221214.0, "max": 229581.0, "mean": 225161.908}, "value": {"min": 0.0666666666666666, "max": 23255.0, "mean": 1429.1537680690162}, "orderid": {"min": 6318.0, "max": 9979604.0, "mean": 5154162.764}, "linkorderid": {"min": 6318.0, "max": 9979604.0, "mean": 5151745.332}, "patientweight": {"min": 42.1, "max": 121.5, "mean": 80.766}, "isopenbag": {"min": 0.0, "max": 1.0, "mean": 0.444}, "continueinnextdept": {"min": 0.0, "max": 0.0, "mean": 0.0}, "ORIGINALAMOUNT": {"min": 0.168058, "max": 23255.0, "mean": 1436.089818074}, "ORIGINALRATE": {"min": 0.0, "max": 1.0, "mean": 0.484}}}}, "comparison_analysis": {"synthetic_data_path": "data\\medical_samples\\compact_sample_records.json", "structure_differences": [], "field_mappings": {"gender": "gender", "timestamp": "timestamp"}, "compatibility_issues": [], "recommendations": [], "synthetic_structure": {"id": {"type": "str", "path": "id"}, "age": {"type": "int", "path": "age"}, "gender": {"type": "str", "path": "gender"}, "dx": {"type": "list", "path": "dx", "nested": {"type": "list", "item_type": "dict", "sample_structure": {"icd": {"type": "str", "path": "dx.icd"}, "seq": {"type": "int", "path": "dx.seq"}}}}, "rx": {"type": "list", "path": "rx", "nested": {"type": "list", "item_type": "dict", "sample_structure": {"drug": {"type": "str", "path": "rx.drug"}, "dose": {"type": "str", "path": "rx.dose"}, "freq": {"type": "str", "path": "rx.freq"}}}}, "labs": {"type": "list", "path": "labs", "nested": {"type": "list", "item_type": "dict", "sample_structure": {"id": {"type": "str", "path": "labs.id"}, "val": {"type": "float", "path": "labs.val"}, "unit": {"type": "str", "path": "labs.unit"}, "flag": {"type": "str", "path": "labs.flag"}}}}, "size": {"type": "int", "path": "size"}, "timestamp": {"type": "str", "path": "timestamp"}, "type": {"type": "str", "path": "type"}}, "missing_fields": ["version", "patient_id", "source", "lab_results", "record_type", "diagnoses", "prescriptions", "anchor_age", "admission"], "extra_fields": ["dx", "type", "size", "labs", "age", "id", "rx"], "type_mismatches": []}, "recommendations": ["📋 Data Structure Recommendations:", "1. Use the existing MIMIC-IV processor for real data integration", "2. Maintain compatibility with synthetic data format for seamless switching", "3. Implement size filtering to ensure records fit steganographic constraints", "4. Add data validation to ensure medical data integrity", "5. Create unified data loading functions for both synthetic and real data"]}
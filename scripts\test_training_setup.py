#!/usr/bin/env python3
"""
Test script to verify the training setup is working correctly.
This script tests all components needed for training without actually training.
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        from config.training_config import get_training_config
        print("✅ Training config import successful")
    except ImportError as e:
        print(f"❌ Training config import failed: {e}")
        return False
    
    try:
        from config.model_config import get_model_config
        print("✅ Model config import successful")
    except ImportError as e:
        print(f"❌ Model config import failed: {e}")
        return False
    
    try:
        from src.models.generator import SteganoGenerator
        from src.models.discriminator import SteganoDiscriminator
        from src.models.decoder import SteganoDecoder
        print("✅ Model imports successful")
    except ImportError as e:
        print(f"❌ Model imports failed: {e}")
        return False
    
    try:
        from src.training.losses import SteganoGANLoss
        print("✅ Loss function import successful")
    except ImportError as e:
        print(f"❌ Loss function import failed: {e}")
        return False
    
    try:
        from src.data.dataset import SteganoDataset, create_dataloader
        print("✅ Dataset imports successful")
    except ImportError as e:
        print(f"❌ Dataset imports failed: {e}")
        return False
    
    return True

def test_configurations():
    """Test that configurations can be loaded."""
    print("\n⚙️  Testing configurations...")
    
    try:
        from config.training_config import get_training_config
        
        # Test unified_real_fast config
        config = get_training_config('unified_real_fast')
        print(f"✅ unified_real_fast config loaded: {config.num_epochs} epochs")
        
        # Test unified_real config
        config = get_training_config('unified_real')
        print(f"✅ unified_real config loaded: {config.num_epochs} epochs")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_dataset_availability():
    """Test that datasets and images are available."""
    print("\n📊 Testing dataset availability...")
    
    # Check unified dataset files
    train_data = Path("data/unified_medical_real/unified_real_train_records.json")
    val_data = Path("data/unified_medical_real/unified_real_val_records.json")
    
    if not train_data.exists():
        print(f"❌ Training dataset not found: {train_data}")
        return False
    print(f"✅ Training dataset found: {train_data}")
    
    if not val_data.exists():
        print(f"❌ Validation dataset not found: {val_data}")
        return False
    print(f"✅ Validation dataset found: {val_data}")
    
    # Check medical images
    image_dir = Path("data/medical_images")
    if not image_dir.exists():
        print(f"❌ Medical images directory not found: {image_dir}")
        return False
    
    modalities = ['chest_xray', 'ct_scan', 'mri', 'ultrasound']
    total_images = 0
    
    for modality in modalities:
        modality_dir = image_dir / modality / "256x256"
        if modality_dir.exists():
            images = list(modality_dir.glob("*.png"))
            total_images += len(images)
            print(f"✅ {modality}: {len(images)} images")
        else:
            print(f"⚠️  {modality} directory not found")
    
    if total_images == 0:
        print("❌ No medical images found")
        return False
    
    print(f"✅ Total medical images: {total_images}")
    return True

def test_model_creation():
    """Test that models can be created."""
    print("\n🏗️  Testing model creation...")
    
    try:
        import torch
        from config.model_config import get_model_config
        from src.models.generator import SteganoGenerator
        from src.models.discriminator import SteganoDiscriminator
        from src.models.decoder import SteganoDecoder
        
        device = torch.device('cpu')  # Use CPU for testing
        model_config = get_model_config('default')
        
        # Test generator creation
        generator = SteganoGenerator(
            input_channels=model_config.generator.input_channels,
            output_channels=model_config.generator.output_channels,
            hidden_channels=model_config.generator.hidden_channels,
            num_blocks=model_config.generator.num_blocks,
            max_data_length=model_config.generator.max_data_length,
            embedding_dim=model_config.generator.embedding_dim,
            use_attention=model_config.generator.use_attention,
            norm_type=model_config.generator.norm_type,
            activation=model_config.generator.activation,
            dropout_rate=model_config.generator.dropout_rate
        ).to(device)
        print("✅ Generator created successfully")
        
        # Test discriminator creation
        discriminator = SteganoDiscriminator(
            input_channels=model_config.discriminator.input_channels,
            hidden_channels=model_config.discriminator.hidden_channels,
            num_layers=model_config.discriminator.num_layers,
            patch_size=model_config.discriminator.patch_size,
            use_multiscale=True,
            num_scales=2,
            norm_type=model_config.discriminator.norm_type,
            activation=model_config.discriminator.activation,
            use_spectral_norm=model_config.discriminator.use_spectral_norm,
            dropout_rate=model_config.discriminator.dropout_rate
        ).to(device)
        print("✅ Discriminator created successfully")
        
        # Test decoder creation
        decoder = SteganoDecoder(
            input_channels=model_config.decoder.input_channels,
            hidden_channels=model_config.decoder.hidden_channels,
            num_layers=model_config.decoder.num_layers,
            output_dim=model_config.decoder.output_dim,
            use_robust=True,
            use_attention=model_config.decoder.use_attention,
            norm_type=model_config.decoder.norm_type,
            activation=model_config.decoder.activation,
            dropout_rate=model_config.decoder.dropout_rate
        ).to(device)
        print("✅ Decoder created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 SteganoGAN Training Setup Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configurations),
        ("Dataset Availability Test", test_dataset_availability),
        ("Model Creation Test", test_model_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Training setup is ready.")
        print("\n🚀 You can now start training with:")
        print("   python scripts/start_training.py --config unified_real_fast")
        return 0
    else:
        print("💥 Some tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())

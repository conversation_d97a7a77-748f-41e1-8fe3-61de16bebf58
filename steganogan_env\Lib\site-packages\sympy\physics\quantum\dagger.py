"""Hermitian conjugation."""

from sympy.core import Expr, sympify
from sympy.functions.elementary.complexes import adjoint

__all__ = [
    'Dagger'
]


class Dagger(adjoint):
    """General Hermitian conjugate operation.

    Explanation
    ===========

    Take the Hermetian conjugate of an argument [1]_. For matrices this
    operation is equivalent to transpose and complex conjugate [2]_.

    Parameters
    ==========

    arg : Expr
        The SymPy expression that we want to take the dagger of.
    evaluate : bool
        Whether the resulting expression should be directly evaluated.

    Examples
    ========

    Daggering various quantum objects:

        >>> from sympy.physics.quantum.dagger import Dagger
        >>> from sympy.physics.quantum.state import Ket, Bra
        >>> from sympy.physics.quantum.operator import Operator
        >>> Dagger(Ket('psi'))
        <psi|
        >>> Dagger(Bra('phi'))
        |phi>
        >>> Dagger(Operator('A'))
        Dagger(A)

    Inner and outer products::

        >>> from sympy.physics.quantum import InnerProduct, OuterProduct
        >>> <PERSON>gger(InnerProduct(Bra('a'), <PERSON><PERSON>('b')))
        <b|a>
        >>> Dagger(OuterProduct(Ket('a'), <PERSON>ra('b')))
        |b><a|

    Powers, sums and products::

        >>> A = Operator('A')
        >>> B = Operator('B')
        >>> Dagger(A*B)
        Dagger(B)*Dagger(A)
        >>> Dagger(A+B)
        Dagger(A) + Dagger(B)
        >>> Dagger(A**2)
        Dagger(A)**2

    Dagger also seamlessly handles complex numbers and matrices::

        >>> from sympy import Matrix, I
        >>> m = Matrix([[1,I],[2,I]])
        >>> m
        Matrix([
        [1, I],
        [2, I]])
        >>> Dagger(m)
        Matrix([
        [ 1,  2],
        [-I, -I]])

    References
    ==========

    .. [1] https://en.wikipedia.org/wiki/Hermitian_adjoint
    .. [2] https://en.wikipedia.org/wiki/Hermitian_transpose
    """

    @property
    def kind(self):
        """Find the kind of a dagger of something (just the kind of the something)."""
        return self.args[0].kind

    def __new__(cls, arg, evaluate=True):
        if hasattr(arg, 'adjoint') and evaluate:
            return arg.adjoint()
        elif hasattr(arg, 'conjugate') and hasattr(arg, 'transpose') and evaluate:
            return arg.conjugate().transpose()
        return Expr.__new__(cls, sympify(arg))

adjoint.__name__ = "Dagger"
adjoint._sympyrepr = lambda a, b: "Dagger(%s)" % b._print(a.args[0])

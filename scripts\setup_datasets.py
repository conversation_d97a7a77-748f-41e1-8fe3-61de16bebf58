#!/usr/bin/env python3
"""
Dataset setup script for medical steganography research.
Downloads and prepares multiple image datasets for cover image training.
"""

import os
import sys
import argparse
import requests
import zipfile
import tarfile
from pathlib import Path
import pandas as pd
from PIL import Image
import numpy as np
from tqdm import tqdm
import json

class DatasetDownloader:
    """Download and prepare datasets for medical steganography."""
    
    def __init__(self, base_dir="data/cover_images"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
    def download_covid_radiography(self):
        """Download COVID-19 Radiography Database from Kaggle."""
        print("📥 Downloading COVID-19 Radiography Database...")
        
        # Note: Requires Kaggle API setup
        dataset_dir = self.base_dir / "covid_radiography"
        dataset_dir.mkdir(exist_ok=True)
        
        print("⚠️  This requires Kaggle API setup:")
        print("1. Install: pip install kaggle")
        print("2. Setup API key: https://www.kaggle.com/docs/api")
        print("3. Run: kaggle datasets download -d tawsifurrahman/covid19-radiography-database")
        
        return dataset_dir
    
    def download_sample_medical_images(self):
        """Download sample medical images for testing."""
        print("📥 Creating sample medical image dataset...")
        
        sample_dir = self.base_dir / "sample_medical"
        sample_dir.mkdir(exist_ok=True)
        
        # Create synthetic medical-like images for testing
        for i in range(100):
            # Create chest X-ray like image
            img_array = self.create_synthetic_xray()
            img = Image.fromarray(img_array)
            img.save(sample_dir / f"sample_xray_{i:03d}.jpg", quality=95)
        
        print(f"✅ Created 100 sample medical images in {sample_dir}")
        return sample_dir
    
    def create_synthetic_xray(self, size=(256, 256)):
        """Create synthetic chest X-ray like image."""
        height, width = size
        
        # Create base lung structure
        img = np.zeros((height, width), dtype=np.uint8)
        
        # Add lung fields (darker regions)
        center_y, center_x = height // 2, width // 2
        
        # Left lung
        y, x = np.ogrid[:height, :width]
        left_lung = ((x - center_x + 60)**2 + (y - center_y)**2) < (80**2)
        img[left_lung] = 40
        
        # Right lung  
        right_lung = ((x - center_x - 60)**2 + (y - center_y)**2) < (80**2)
        img[right_lung] = 40
        
        # Add ribs (brighter lines)
        for i in range(5):
            rib_y = center_y - 60 + i * 30
            if 0 <= rib_y < height:
                img[rib_y:rib_y+2, center_x-80:center_x+80] = 120
        
        # Add noise for texture
        noise = np.random.normal(0, 10, (height, width))
        img = np.clip(img + noise, 0, 255).astype(np.uint8)
        
        # Convert to RGB
        img_rgb = np.stack([img, img, img], axis=2)
        
        return img_rgb
    
    def prepare_celeba_hq_info(self):
        """Provide information for CelebA-HQ download."""
        print("📋 CelebA-HQ Dataset Information:")
        print("   Size: 30,000 high-quality face images")
        print("   Resolution: 1024×1024")
        print("   Download: https://github.com/tkarras/progressive_growing_of_gans")
        print("   Note: Requires manual download and extraction")
        
        celeba_dir = self.base_dir / "celeba_hq"
        celeba_dir.mkdir(exist_ok=True)
        
        # Create instructions file
        instructions = """
# CelebA-HQ Download Instructions

1. Visit: https://github.com/tkarras/progressive_growing_of_gans
2. Follow the data preparation instructions
3. Download the dataset files
4. Extract to this directory: {celeba_dir}
5. Organize as: celeba_hq/images/00000.jpg, 00001.jpg, etc.

Expected structure:
celeba_hq/
├── images/
│   ├── 00000.jpg
│   ├── 00001.jpg
│   └── ...
└── README.txt
        """.format(celeba_dir=celeba_dir)
        
        with open(celeba_dir / "DOWNLOAD_INSTRUCTIONS.txt", "w") as f:
            f.write(instructions)
        
        return celeba_dir
    
    def prepare_chest_xray_info(self):
        """Provide information for ChestX-ray14 download."""
        print("📋 ChestX-ray14 Dataset Information:")
        print("   Size: 112,120 chest X-ray images")
        print("   Resolution: 1024×1024")
        print("   Download: https://nihcc.app.box.com/v/ChestXray-NIHCC")
        print("   License: Public domain")
        
        chest_dir = self.base_dir / "chest_xray14"
        chest_dir.mkdir(exist_ok=True)
        
        instructions = """
# ChestX-ray14 Download Instructions

1. Visit: https://nihcc.app.box.com/v/ChestXray-NIHCC
2. Download the image files (multiple zip files)
3. Extract all images to: {chest_dir}/images/
4. Download the metadata CSV files
5. Place metadata in: {chest_dir}/metadata/

Expected structure:
chest_xray14/
├── images/
│   ├── 00000001_000.png
│   ├── 00000001_001.png
│   └── ...
├── metadata/
│   ├── Data_Entry_2017.csv
│   └── ...
└── README.txt

Note: Total download size is approximately 45GB
        """.format(chest_dir=chest_dir)
        
        with open(chest_dir / "DOWNLOAD_INSTRUCTIONS.txt", "w") as f:
            f.write(instructions)
        
        return chest_dir
    
    def create_dataset_config(self):
        """Create configuration file for available datasets."""
        config = {
            "datasets": {
                "sample_medical": {
                    "path": str(self.base_dir / "sample_medical"),
                    "type": "synthetic_medical",
                    "size": 100,
                    "resolution": [256, 256],
                    "channels": 3,
                    "ready": True
                },
                "covid_radiography": {
                    "path": str(self.base_dir / "covid_radiography"),
                    "type": "chest_xray",
                    "size": 21165,
                    "resolution": [512, 512],
                    "channels": 3,
                    "ready": False,
                    "download_url": "kaggle datasets download -d tawsifurrahman/covid19-radiography-database"
                },
                "chest_xray14": {
                    "path": str(self.base_dir / "chest_xray14"),
                    "type": "chest_xray",
                    "size": 112120,
                    "resolution": [1024, 1024],
                    "channels": 1,
                    "ready": False,
                    "download_url": "https://nihcc.app.box.com/v/ChestXray-NIHCC"
                },
                "celeba_hq": {
                    "path": str(self.base_dir / "celeba_hq"),
                    "type": "faces",
                    "size": 30000,
                    "resolution": [1024, 1024],
                    "channels": 3,
                    "ready": False,
                    "download_url": "https://github.com/tkarras/progressive_growing_of_gans"
                }
            },
            "recommended_combinations": {
                "medical_focus": ["chest_xray14", "covid_radiography"],
                "general_robustness": ["celeba_hq", "chest_xray14"],
                "development": ["sample_medical"],
                "comprehensive": ["chest_xray14", "celeba_hq", "covid_radiography"]
            }
        }
        
        config_path = self.base_dir / "dataset_config.json"
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
        
        print(f"📋 Dataset configuration saved to: {config_path}")
        return config
    
    def validate_dataset(self, dataset_name):
        """Validate that a dataset is properly downloaded and organized."""
        dataset_path = self.base_dir / dataset_name
        
        if not dataset_path.exists():
            return False, f"Dataset directory {dataset_path} not found"
        
        if dataset_name == "sample_medical":
            images = list(dataset_path.glob("*.jpg"))
            if len(images) < 50:
                return False, f"Expected at least 50 images, found {len(images)}"
            return True, f"Found {len(images)} sample medical images"
        
        elif dataset_name in ["chest_xray14", "covid_radiography"]:
            images_dir = dataset_path / "images"
            if not images_dir.exists():
                return False, f"Images directory not found: {images_dir}"
            
            images = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg"))
            if len(images) == 0:
                return False, "No image files found"
            
            return True, f"Found {len(images)} medical images"
        
        elif dataset_name == "celeba_hq":
            images_dir = dataset_path / "images"
            if not images_dir.exists():
                return False, f"Images directory not found: {images_dir}"
            
            images = list(images_dir.glob("*.jpg"))
            if len(images) < 1000:
                return False, f"Expected at least 1000 images, found {len(images)}"
            
            return True, f"Found {len(images)} face images"
        
        return False, "Unknown dataset type"


def main():
    parser = argparse.ArgumentParser(description='Setup datasets for medical steganography')
    parser.add_argument('--base_dir', type=str, default='data/cover_images',
                       help='Base directory for datasets')
    parser.add_argument('--action', type=str, default='setup',
                       choices=['setup', 'validate', 'info'],
                       help='Action to perform')
    parser.add_argument('--dataset', type=str, default=None,
                       help='Specific dataset to validate')
    
    args = parser.parse_args()
    
    downloader = DatasetDownloader(args.base_dir)
    
    if args.action == 'setup':
        print("🚀 Setting up datasets for medical steganography...")
        
        # Create sample dataset for immediate use
        downloader.download_sample_medical_images()
        
        # Prepare information for manual downloads
        downloader.prepare_chest_xray_info()
        downloader.prepare_celeba_hq_info()
        downloader.download_covid_radiography()
        
        # Create configuration
        config = downloader.create_dataset_config()
        
        print("\n✅ Dataset setup complete!")
        print("📁 Check the following directories:")
        print(f"   - Sample medical images: {downloader.base_dir}/sample_medical")
        print(f"   - Download instructions: {downloader.base_dir}/*/DOWNLOAD_INSTRUCTIONS.txt")
        print(f"   - Configuration: {downloader.base_dir}/dataset_config.json")
        
    elif args.action == 'validate':
        if args.dataset:
            valid, message = downloader.validate_dataset(args.dataset)
            status = "✅" if valid else "❌"
            print(f"{status} {args.dataset}: {message}")
        else:
            print("🔍 Validating all datasets...")
            datasets = ["sample_medical", "chest_xray14", "covid_radiography", "celeba_hq"]
            for dataset in datasets:
                valid, message = downloader.validate_dataset(dataset)
                status = "✅" if valid else "❌"
                print(f"{status} {dataset}: {message}")
    
    elif args.action == 'info':
        print("📊 Dataset Information for Medical Steganography")
        print("=" * 60)
        
        datasets_info = [
            ("ChestX-ray14", "112K chest X-rays", "Medical imaging", "45GB"),
            ("COVID-19 Radiography", "21K chest X-rays", "Medical imaging", "2GB"),
            ("CelebA-HQ", "30K face images", "Natural images", "5GB"),
            ("Sample Medical", "100 synthetic", "Development", "50MB")
        ]
        
        for name, size, category, download_size in datasets_info:
            print(f"{name:<20} | {size:<15} | {category:<15} | {download_size}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Convenient script to start SteganoGAN training with the unified medical dataset.
This script sets up everything needed and launches training immediately.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

def check_prerequisites():
    """Check if all prerequisites are available."""
    print("🔍 Checking prerequisites...")
    
    # Check if unified dataset exists
    unified_train = Path("data/unified_medical_real/unified_real_train_records.json")
    unified_val = Path("data/unified_medical_real/unified_real_val_records.json")
    
    if not unified_train.exists():
        print(f"❌ Training dataset not found: {unified_train}")
        return False
    
    if not unified_val.exists():
        print(f"❌ Validation dataset not found: {unified_val}")
        return False
    
    print(f"✅ Training dataset found: {unified_train}")
    print(f"✅ Validation dataset found: {unified_val}")
    
    # Check medical images
    image_dir = Path("data/medical_images")
    if not image_dir.exists():
        print(f"❌ Medical images directory not found: {image_dir}")
        return False
    
    # Count images in each modality
    modalities = ['chest_xray', 'ct_scan', 'mri', 'ultrasound']
    total_images = 0
    
    for modality in modalities:
        modality_dir = image_dir / modality / "256x256"
        if modality_dir.exists():
            images = list(modality_dir.glob("*.png"))
            total_images += len(images)
            print(f"✅ {modality}: {len(images)} images")
        else:
            print(f"⚠️  {modality} directory not found")
    
    if total_images == 0:
        print("❌ No medical images found")
        return False
    
    print(f"✅ Total medical images: {total_images}")
    
    # Check if models directory exists, create if not
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # Check if logs directory exists, create if not
    logs_dir = Path("results/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    return True

def setup_environment():
    """Set up the training environment."""
    print("\n🛠️  Setting up training environment...")
    
    # Set environment variables for better performance
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'
    os.environ['TORCH_CUDNN_V8_API_ENABLED'] = '1'
    
    # Create necessary directories
    directories = [
        "models/unified_real",
        "models/unified_real_fast", 
        "results/logs/unified_real",
        "results/logs/unified_real_fast",
        "results/checkpoints",
        "results/visualizations"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def get_training_command(config_name, device, resume_checkpoint=None):
    """Generate the training command."""
    cmd = [
        sys.executable, "scripts/train.py",
        "--config", config_name,
        "--device", device
    ]
    
    if resume_checkpoint:
        cmd.extend(["--resume", resume_checkpoint])
    
    return cmd

def main():
    parser = argparse.ArgumentParser(description='Start SteganoGAN Training')
    parser.add_argument('--config', type=str, default='unified_real_fast',
                       choices=['unified_real', 'unified_real_fast'],
                       help='Training configuration to use')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    parser.add_argument('--resume', type=str, default=None,
                       help='Resume from checkpoint')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without actually training')
    
    args = parser.parse_args()
    
    print("🚀 SteganoGAN Training Launcher")
    print("=" * 60)
    print(f"📅 Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⚙️  Configuration: {args.config}")
    print(f"🖥️  Device: {args.device}")
    if args.resume:
        print(f"🔄 Resume from: {args.resume}")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix the issues above.")
        return 1
    
    # Setup environment
    setup_environment()
    
    # Generate training command
    cmd = get_training_command(args.config, args.device, args.resume)
    
    print(f"\n🎯 Training Command:")
    print(f"   {' '.join(cmd)}")
    
    if args.dry_run:
        print("\n🔍 Dry run mode - not actually starting training")
        return 0
    
    # Configuration details
    config_details = {
        'unified_real': {
            'epochs': 250,
            'batch_size': 16,
            'description': 'Full training with progressive resolution',
            'estimated_time': '12-24 hours (depending on GPU)'
        },
        'unified_real_fast': {
            'epochs': 100,
            'batch_size': 20,
            'description': 'Fast training for testing and development',
            'estimated_time': '4-8 hours (depending on GPU)'
        }
    }
    
    config_info = config_details.get(args.config, {})
    print(f"\n📋 Training Configuration Details:")
    print(f"   Epochs: {config_info.get('epochs', 'Unknown')}")
    print(f"   Batch Size: {config_info.get('batch_size', 'Unknown')}")
    print(f"   Description: {config_info.get('description', 'No description')}")
    print(f"   Estimated Time: {config_info.get('estimated_time', 'Unknown')}")
    
    # Start training
    print(f"\n🚀 Starting training...")
    print(f"📊 Monitor progress with: tensorboard --logdir results/logs/{args.config}")
    print(f"💾 Checkpoints will be saved to: models/{args.config}/")
    print("=" * 60)
    
    try:
        # Start the training process
        result = subprocess.run(cmd, check=True)
        print(f"\n🎉 Training completed successfully!")
        return result.returncode
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print(f"\n⚠️  Training interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

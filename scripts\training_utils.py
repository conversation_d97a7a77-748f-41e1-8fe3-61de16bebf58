#!/usr/bin/env python3
"""
Utility scripts for managing SteganoGAN training, checkpoints, and monitoring.
"""

import argparse
import json
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt

# Add project root and src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


def list_checkpoints(checkpoint_dir: str):
    """List all available checkpoints with details."""
    from scripts.train import CheckpointManager
    
    checkpoint_dir = Path(checkpoint_dir)
    if not checkpoint_dir.exists():
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return
    
    # Find all checkpoint subdirectories
    checkpoint_subdirs = [d for d in checkpoint_dir.iterdir() if d.is_dir() and d.name.startswith('checkpoints_')]
    
    if not checkpoint_subdirs:
        print(f"❌ No checkpoint directories found in: {checkpoint_dir}")
        return
    
    print(f"📁 Found {len(checkpoint_subdirs)} checkpoint directories:")
    print()
    
    for subdir in sorted(checkpoint_subdirs):
        print(f"📂 {subdir.name}")
        try:
            manager = CheckpointManager(str(subdir))
            checkpoints = manager.list_checkpoints()
            
            if checkpoints:
                print(f"   Checkpoints: {len(checkpoints)}")
                latest = checkpoints[-1]
                print(f"   Latest: Epoch {latest['epoch']} ({latest['timestamp']})")
                print(f"   Train Loss: Gen={latest['train_metrics'].get('gen_loss', 'N/A'):.4f}, "
                      f"Disc={latest['train_metrics'].get('disc_loss', 'N/A'):.4f}")
                
                if latest['val_metrics']:
                    print(f"   Val Loss: Gen={latest['val_metrics'].get('gen_loss', 'N/A'):.4f}, "
                          f"Disc={latest['val_metrics'].get('disc_loss', 'N/A'):.4f}")
                
                # Check for best checkpoint
                best_path = manager.get_best_checkpoint_path()
                if best_path:
                    print(f"   🏆 Best checkpoint available")
            else:
                print(f"   No checkpoints found")
                
        except Exception as e:
            print(f"   ❌ Error reading checkpoints: {e}")
        
        print()


def view_training_progress(log_dir: str, save_plot: bool = False):
    """View training progress from logs."""
    log_dir = Path(log_dir)
    
    # Find all training subdirectories
    training_subdirs = [d for d in log_dir.iterdir() if d.is_dir() and d.name.startswith('training_')]
    
    if not training_subdirs:
        print(f"❌ No training directories found in: {log_dir}")
        return
    
    print(f"📊 Found {len(training_subdirs)} training runs:")
    print()
    
    for subdir in sorted(training_subdirs):
        print(f"📈 {subdir.name}")
        
        # Check for CSV file
        csv_file = subdir / "training_metrics.csv"
        if csv_file.exists():
            try:
                df = pd.read_csv(csv_file)
                # Filter epoch-level metrics (batch = -1)
                epoch_df = df[df['batch'] == -1]
                
                if len(epoch_df) > 0:
                    latest = epoch_df.iloc[-1]
                    print(f"   Epochs: {len(epoch_df)}")
                    print(f"   Latest: Epoch {int(latest['epoch'])}")
                    print(f"   Train Loss: Gen={latest['train_gen_loss']:.4f}, Disc={latest['train_disc_loss']:.4f}")
                    if latest['val_gen_loss'] > 0:
                        print(f"   Val Loss: Gen={latest['val_gen_loss']:.4f}, Disc={latest['val_disc_loss']:.4f}")
                    print(f"   Total Time: {latest['total_time']:.1f}s")
                    
                    # Plot if requested
                    if save_plot and len(epoch_df) > 1:
                        plot_training_curves(epoch_df, subdir / "training_curves.png")
                        print(f"   📊 Plot saved: training_curves.png")
                
            except Exception as e:
                print(f"   ❌ Error reading CSV: {e}")
        
        # Check for loss history JSON
        json_file = subdir / "loss_history.json"
        if json_file.exists():
            try:
                with open(json_file, 'r') as f:
                    history = json.load(f)
                
                print(f"   Duration: {history.get('training_duration', 0):.1f}s")
                print(f"   Total Epochs: {history.get('total_epochs', 0)}")
                
                if 'best_metrics' in history:
                    best = history['best_metrics']
                    for metric, info in best.items():
                        print(f"   🏆 Best {metric}: {info['value']:.4f} at epoch {info['epoch']}")
                        
            except Exception as e:
                print(f"   ❌ Error reading JSON: {e}")
        
        print()


def plot_training_curves(df, output_path):
    """Plot training curves from DataFrame."""
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('SteganoGAN Training Progress')
    
    # Generator Loss
    axes[0, 0].plot(df['epoch'], df['train_gen_loss'], label='Train', color='blue')
    if 'val_gen_loss' in df.columns and df['val_gen_loss'].sum() > 0:
        val_data = df[df['val_gen_loss'] > 0]
        axes[0, 0].plot(val_data['epoch'], val_data['val_gen_loss'], label='Validation', color='red')
    axes[0, 0].set_title('Generator Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # Discriminator Loss
    axes[0, 1].plot(df['epoch'], df['train_disc_loss'], label='Train', color='blue')
    if 'val_disc_loss' in df.columns and df['val_disc_loss'].sum() > 0:
        val_data = df[df['val_disc_loss'] > 0]
        axes[0, 1].plot(val_data['epoch'], val_data['val_disc_loss'], label='Validation', color='red')
    axes[0, 1].set_title('Discriminator Loss')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # Data Confidence
    axes[1, 0].plot(df['epoch'], df['train_data_confidence'], label='Train', color='green')
    if 'val_data_confidence' in df.columns and df['val_data_confidence'].sum() > 0:
        val_data = df[df['val_data_confidence'] > 0]
        axes[1, 0].plot(val_data['epoch'], val_data['val_data_confidence'], label='Validation', color='orange')
    axes[1, 0].set_title('Data Confidence')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('Confidence')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # Learning Rates
    axes[1, 1].plot(df['epoch'], df['learning_rate_gen'], label='Generator', color='purple')
    axes[1, 1].plot(df['epoch'], df['learning_rate_disc'], label='Discriminator', color='brown')
    axes[1, 1].set_title('Learning Rates')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Learning Rate')
    axes[1, 1].legend()
    axes[1, 1].grid(True)
    axes[1, 1].set_yscale('log')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()


def resume_training_helper(checkpoint_dir: str):
    """Helper to find the best checkpoint to resume from."""
    from scripts.train import CheckpointManager
    
    checkpoint_dir = Path(checkpoint_dir)
    if not checkpoint_dir.exists():
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return None
    
    # Find latest checkpoint directory
    checkpoint_subdirs = [d for d in checkpoint_dir.iterdir() if d.is_dir() and d.name.startswith('checkpoints_')]
    
    if not checkpoint_subdirs:
        print(f"❌ No checkpoint directories found")
        return None
    
    latest_subdir = max(checkpoint_subdirs, key=lambda d: d.stat().st_mtime)
    print(f"📂 Latest checkpoint directory: {latest_subdir.name}")
    
    manager = CheckpointManager(str(latest_subdir))
    
    # Check for best checkpoint first
    best_path = manager.get_best_checkpoint_path()
    if best_path:
        print(f"🏆 Best checkpoint found: {best_path}")
        return str(best_path)
    
    # Otherwise, get latest checkpoint
    latest_path = manager.get_latest_checkpoint_path()
    if latest_path:
        print(f"📄 Latest checkpoint found: {latest_path}")
        return str(latest_path)
    
    print("❌ No checkpoints found")
    return None


def main():
    parser = argparse.ArgumentParser(description='SteganoGAN Training Utilities')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List checkpoints command
    list_parser = subparsers.add_parser('list', help='List available checkpoints')
    list_parser.add_argument('checkpoint_dir', help='Checkpoint directory path')
    
    # View progress command
    progress_parser = subparsers.add_parser('progress', help='View training progress')
    progress_parser.add_argument('log_dir', help='Log directory path')
    progress_parser.add_argument('--plot', action='store_true', help='Save training curve plots')
    
    # Resume helper command
    resume_parser = subparsers.add_parser('resume', help='Find best checkpoint to resume from')
    resume_parser.add_argument('checkpoint_dir', help='Checkpoint directory path')
    
    args = parser.parse_args()
    
    if args.command == 'list':
        list_checkpoints(args.checkpoint_dir)
    elif args.command == 'progress':
        view_training_progress(args.log_dir, args.plot)
    elif args.command == 'resume':
        checkpoint_path = resume_training_helper(args.checkpoint_dir)
        if checkpoint_path:
            print(f"\n🚀 To resume training, use:")
            print(f"python scripts/train.py --resume \"{checkpoint_path}\"")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()

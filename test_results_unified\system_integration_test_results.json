{"test_timestamp": "2025-05-26T07:37:46.283399", "configuration": {"synthetic_data_path": "data\\unified_medical_real\\unified_real_sample_records.json", "image_dir": "data\\medical_images", "mimic_path": null, "output_dir": "test_results_unified"}, "tests": {"data_loading": {"status": "passed", "synthetic_data": {"loaded": true, "record_count": 20, "sample_record_size": 504, "average_size": 465.1}, "real_data": {}, "errors": []}, "image_loading": {"status": "passed", "image_types": {"chest_xray": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "ct_scan": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "ultrasound": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "mri": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}}, "errors": []}, "data_preprocessing": {"status": "failed", "medical_processor": {}, "image_transforms": {"available": true, "transform_successful": true, "output_shape": [3, 256, 256]}, "errors": ["Medical processor test failed: MedicalDataProcessor.__init__() got an unexpected keyword argument 'max_data_length'"]}, "steganography_simulation": {"status": "passed", "embedding_test": {"successful": true, "data_size": 99, "capacity_sufficient": true, "utilization_percent": 0.40283203125}, "extraction_test": {"successful": true, "data_integrity": true}, "quality_metrics": {"mse": 0.0018463134765625, "psnr": 75.4677492117516, "quality_acceptable": "True"}, "errors": []}, "end_to_end_pipeline": {"status": "passed", "pipeline_steps": {"data_loading": {"successful": true}, "image_loading": {"successful": true}, "data_encoding": {"successful": true, "encoded_size": 446}, "steganographic_embedding": {"successful": true}, "data_extraction": {"successful": true}}, "performance_metrics": {"total_time_seconds": 0.028929948806762695, "steps_completed": 5}, "errors": []}}, "summary": {"total_tests": 5, "passed_tests": 4, "failed_tests": 1, "success_rate": 0.8, "overall_status": "failed"}}
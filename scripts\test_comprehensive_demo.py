#!/usr/bin/env python3
"""
Test script for the comprehensive demo to verify it works correctly.
This script runs a quick test with minimal parameters.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "src"))

def test_comprehensive_demo():
    """Test the comprehensive demo with minimal parameters."""
    print("🧪 Testing Comprehensive Demo Script")
    print("=" * 50)

    # Import the demo runner
    try:
        from scripts.comprehensive_demo import ComprehensiveDemoRunner
        print("✅ Successfully imported ComprehensiveDemoRunner")
    except ImportError as e:
        print(f"❌ Failed to import ComprehensiveDemoRunner: {e}")
        return False

    # Test configuration
    config = {
        'dataset_path': 'data/unified_medical_real/unified_real_sample_records.json',
        'checkpoint': None,
        'model_config': 'default',
        'device': 'cpu',  # Use CPU for testing
        'output_dir': 'test_demo_results',
        'num_records': 2,  # Test with just 2 records
        'num_images_per_modality': 1,  # Test with just 1 image per modality
        'generate_report': True
    }

    try:
        # Initialize demo runner
        print("\n🚀 Initializing demo runner...")
        demo_runner = ComprehensiveDemoRunner(config)
        print("✅ Demo runner initialized successfully")

        # Check if medical images exist
        print("\n📸 Checking medical image availability...")
        for modality in demo_runner.image_modalities.keys():
            images = demo_runner.get_available_images(modality)
            print(f"   {modality}: {len(images)} images available")

        # Load dataset
        print("\n📊 Loading medical dataset...")
        records = demo_runner.load_unified_dataset()
        print(f"✅ Loaded {len(records)} medical records")

        print("\n🎯 Test completed successfully!")
        print("   The comprehensive demo script is ready to use.")
        print(f"\n📝 To run the full demo, use:")
        print(f"   python scripts/comprehensive_demo.py --num_records 5 --num_images_per_modality 3")

        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    success = test_comprehensive_demo()

    if success:
        print("\n🎉 All tests passed! The comprehensive demo is ready to use.")
        exit(0)
    else:
        print("\n💥 Tests failed. Please check the errors above.")
        exit(1)

if __name__ == "__main__":
    main()

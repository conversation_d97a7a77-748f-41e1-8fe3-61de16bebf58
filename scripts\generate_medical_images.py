#!/usr/bin/env python3
"""
Generate realistic medical images for SteganoGAN development and testing.
Creates various types of medical images suitable as cover images for steganographic embedding.
"""

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFilter
import random
import argparse
from pathlib import Path
from typing import Tuple, List, Dict, Any
import json
from datetime import datetime


class MedicalImageGenerator:
    """Generate realistic-looking medical images for development purposes."""

    def __init__(self, seed: int = 42):
        """Initialize generator with random seed."""
        random.seed(seed)
        np.random.seed(seed)

    def generate_chest_xray(self, size: Tuple[int, int] = (256, 256)) -> Image.Image:
        """Generate a realistic chest X-ray image."""

        width, height = size

        # Create base image with lung field background
        img_array = np.zeros((height, width), dtype=np.uint8)

        # Add lung fields
        center_x, center_y = width // 2, height // 2

        # Left lung field
        left_lung_x = center_x - width // 6
        left_lung_y = center_y
        self._add_lung_field(img_array, left_lung_x, left_lung_y, width//4, height//3)

        # Right lung field
        right_lung_x = center_x + width // 6
        right_lung_y = center_y
        self._add_lung_field(img_array, right_lung_x, right_lung_y, width//4, height//3)

        # Add ribcage structure
        self._add_ribcage(img_array, center_x, center_y, width, height)

        # Add heart shadow
        self._add_heart_shadow(img_array, center_x - width//8, center_y + height//8, width//6, height//6)

        # Add medical noise and texture
        self._add_medical_noise(img_array, intensity=0.1)

        # Convert to RGB
        rgb_array = np.stack([img_array, img_array, img_array], axis=2)

        return Image.fromarray(rgb_array)

    def generate_ct_scan(self, size: Tuple[int, int] = (256, 256)) -> Image.Image:
        """Generate a realistic CT scan image."""

        width, height = size
        img_array = np.zeros((height, width), dtype=np.uint8)

        # Add brain/body outline
        center_x, center_y = width // 2, height // 2

        # Outer body contour
        self._add_circular_structure(img_array, center_x, center_y, min(width, height)//3, 120)

        # Internal structures
        for _ in range(5):
            x = center_x + random.randint(-width//4, width//4)
            y = center_y + random.randint(-height//4, height//4)
            radius = random.randint(10, 30)
            intensity = random.randint(80, 180)
            self._add_circular_structure(img_array, x, y, radius, intensity)

        # Add CT-specific noise
        self._add_medical_noise(img_array, intensity=0.15)

        # Convert to RGB
        rgb_array = np.stack([img_array, img_array, img_array], axis=2)

        return Image.fromarray(rgb_array)

    def generate_ultrasound(self, size: Tuple[int, int] = (256, 256)) -> Image.Image:
        """Generate a realistic ultrasound image."""

        width, height = size

        # Create fan-shaped ultrasound field
        img_array = np.zeros((height, width), dtype=np.uint8)

        # Add ultrasound fan pattern
        center_x = width // 2
        center_y = height - 20

        for y in range(height):
            for x in range(width):
                # Distance from ultrasound source
                dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                angle = np.arctan2(y - center_y, x - center_x)

                # Create fan pattern
                if abs(angle) < np.pi/3 and dist < height * 0.8:
                    # Add ultrasound texture
                    noise = np.random.normal(0, 20)
                    intensity = 60 + 40 * np.sin(dist/10) + noise
                    intensity = max(0, min(255, intensity))
                    img_array[y, x] = int(intensity)

        # Add some anatomical structures
        self._add_ultrasound_structures(img_array, center_x, center_y)

        # Convert to RGB
        rgb_array = np.stack([img_array, img_array, img_array], axis=2)

        return Image.fromarray(rgb_array)

    def generate_mri_scan(self, size: Tuple[int, int] = (256, 256)) -> Image.Image:
        """Generate a realistic MRI scan image."""

        width, height = size
        img_array = np.zeros((height, width), dtype=np.uint8)

        center_x, center_y = width // 2, height // 2

        # Add brain outline for brain MRI
        self._add_circular_structure(img_array, center_x, center_y, min(width, height)//3, 100)

        # Add brain structures
        # Gray matter
        self._add_circular_structure(img_array, center_x, center_y, min(width, height)//4, 140)

        # White matter regions
        for _ in range(8):
            x = center_x + random.randint(-width//6, width//6)
            y = center_y + random.randint(-height//6, height//6)
            radius = random.randint(8, 20)
            self._add_circular_structure(img_array, x, y, radius, 180)

        # Add MRI-specific characteristics
        self._add_medical_noise(img_array, intensity=0.05)

        # Convert to RGB
        rgb_array = np.stack([img_array, img_array, img_array], axis=2)

        return Image.fromarray(rgb_array)

    def _add_lung_field(self, img_array: np.ndarray, center_x: int, center_y: int,
                       width: int, height: int):
        """Add lung field to chest X-ray."""

        h, w = img_array.shape

        for y in range(max(0, center_y - height//2), min(h, center_y + height//2)):
            for x in range(max(0, center_x - width//2), min(w, center_x + width//2)):
                # Create lung texture
                dist_x = abs(x - center_x) / (width/2)
                dist_y = abs(y - center_y) / (height/2)

                if dist_x**2 + dist_y**2 < 1:  # Elliptical lung field
                    base_intensity = 160
                    texture = 20 * np.sin(x/8) * np.cos(y/8)
                    noise = np.random.normal(0, 10)
                    intensity = base_intensity + texture + noise
                    img_array[y, x] = max(0, min(255, int(intensity)))

    def _add_ribcage(self, img_array: np.ndarray, center_x: int, center_y: int,
                    width: int, height: int):
        """Add ribcage structure to chest X-ray."""

        # Add several rib lines
        for i in range(6):
            rib_y = center_y - height//3 + i * height//10
            if 0 <= rib_y < img_array.shape[0]:
                # Create curved rib line
                for x in range(width):
                    curve_offset = int(15 * np.sin(np.pi * x / width))
                    y = rib_y + curve_offset
                    if 0 <= y < img_array.shape[0]:
                        # Make ribs slightly brighter (bone density)
                        img_array[y, x] = min(255, img_array[y, x] + 30)

    def _add_heart_shadow(self, img_array: np.ndarray, center_x: int, center_y: int,
                         width: int, height: int):
        """Add heart shadow to chest X-ray."""

        h, w = img_array.shape

        for y in range(max(0, center_y - height//2), min(h, center_y + height//2)):
            for x in range(max(0, center_x - width//2), min(w, center_x + width//2)):
                dist_x = abs(x - center_x) / (width/2)
                dist_y = abs(y - center_y) / (height/2)

                # Heart-shaped region
                if dist_x**2 + dist_y**2 < 1:
                    # Darken for heart shadow
                    img_array[y, x] = max(0, img_array[y, x] - 40)

    def _add_circular_structure(self, img_array: np.ndarray, center_x: int, center_y: int,
                               radius: int, intensity: int):
        """Add circular anatomical structure."""

        h, w = img_array.shape

        for y in range(max(0, center_y - radius), min(h, center_y + radius)):
            for x in range(max(0, center_x - radius), min(w, center_x + radius)):
                dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                if dist <= radius:
                    # Smooth falloff from center
                    falloff = 1 - (dist / radius)
                    img_array[y, x] = max(img_array[y, x], int(intensity * falloff))

    def _add_ultrasound_structures(self, img_array: np.ndarray, center_x: int, center_y: int):
        """Add anatomical structures to ultrasound image."""

        # Add some organ-like structures
        for _ in range(3):
            x = center_x + random.randint(-50, 50)
            y = center_y - random.randint(50, 150)
            if 0 <= x < img_array.shape[1] and 0 <= y < img_array.shape[0]:
                radius = random.randint(15, 30)
                intensity = random.randint(100, 200)
                self._add_circular_structure(img_array, x, y, radius, intensity)

    def _add_medical_noise(self, img_array: np.ndarray, intensity: float = 0.1):
        """Add realistic medical imaging noise."""

        noise = np.random.normal(0, intensity * 255, img_array.shape)
        img_array[:] = np.clip(img_array + noise, 0, 255).astype(np.uint8)


def generate_medical_image_dataset(
    output_dir: str,
    num_images_per_type: int = 50,
    image_sizes: List[Tuple[int, int]] = [(256, 256), (512, 512)],
    image_types: List[str] = ['chest_xray', 'ct_scan', 'ultrasound', 'mri']
) -> Dict[str, Any]:
    """Generate a complete dataset of medical images."""

    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    generator = MedicalImageGenerator()

    dataset_info = {
        "generation_timestamp": datetime.now().isoformat(),
        "total_images": 0,
        "image_types": {},
        "sizes": image_sizes,
        "file_paths": []
    }

    print(f"🏥 Generating Medical Image Dataset...")
    print(f"📁 Output Directory: {output_dir}")
    print(f"🖼️  Image Types: {image_types}")
    print(f"📏 Image Sizes: {image_sizes}")
    print(f"🔢 Images per Type: {num_images_per_type}")

    for img_type in image_types:
        type_dir = output_dir / img_type
        type_dir.mkdir(exist_ok=True)

        type_info = {
            "count": 0,
            "sizes": {},
            "files": []
        }

        print(f"\n📋 Generating {img_type} images...")

        for size in image_sizes:
            size_dir = type_dir / f"{size[0]}x{size[1]}"
            size_dir.mkdir(exist_ok=True)

            size_files = []

            for i in range(num_images_per_type):
                # Generate image
                if img_type == 'chest_xray':
                    img = generator.generate_chest_xray(size)
                elif img_type == 'ct_scan':
                    img = generator.generate_ct_scan(size)
                elif img_type == 'ultrasound':
                    img = generator.generate_ultrasound(size)
                elif img_type == 'mri':
                    img = generator.generate_mri_scan(size)
                else:
                    continue

                # Save image
                filename = f"{img_type}_{size[0]}x{size[1]}_{i+1:03d}.png"
                file_path = size_dir / filename
                img.save(file_path)

                size_files.append(str(file_path))
                type_info["files"].append(str(file_path))
                dataset_info["file_paths"].append(str(file_path))

                if (i + 1) % 10 == 0:
                    print(f"   Generated {i+1}/{num_images_per_type} {size[0]}x{size[1]} images")

            type_info["sizes"][f"{size[0]}x{size[1]}"] = {
                "count": len(size_files),
                "files": size_files
            }
            type_info["count"] += len(size_files)

        dataset_info["image_types"][img_type] = type_info
        dataset_info["total_images"] += type_info["count"]

        print(f"   ✅ Generated {type_info['count']} {img_type} images")

    # Save dataset configuration
    config_path = output_dir / "medical_images_config.json"
    with open(config_path, 'w') as f:
        json.dump(dataset_info, f, indent=2)

    print(f"\n📊 Dataset Generation Complete!")
    print(f"✅ Total Images: {dataset_info['total_images']}")
    print(f"📄 Configuration: {config_path}")

    return dataset_info


def main():
    parser = argparse.ArgumentParser(description='Generate medical images for SteganoGAN development')
    parser.add_argument('--output_dir', type=str, default='data/medical_images',
                       help='Output directory for generated images')
    parser.add_argument('--num_per_type', type=int, default=50,
                       help='Number of images per type and size')
    parser.add_argument('--sizes', type=str, nargs='+', default=['256x256', '512x512'],
                       help='Image sizes (format: WIDTHxHEIGHT)')
    parser.add_argument('--types', type=str, nargs='+',
                       default=['chest_xray', 'ct_scan', 'ultrasound', 'mri'],
                       help='Types of medical images to generate')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')

    args = parser.parse_args()

    # Parse sizes
    image_sizes = []
    for size_str in args.sizes:
        try:
            width, height = map(int, size_str.split('x'))
            image_sizes.append((width, height))
        except ValueError:
            print(f"❌ Invalid size format: {size_str}. Use WIDTHxHEIGHT format.")
            return

    # Generate dataset
    dataset_info = generate_medical_image_dataset(
        output_dir=args.output_dir,
        num_images_per_type=args.num_per_type,
        image_sizes=image_sizes,
        image_types=args.types
    )

    print(f"\n🚀 Next Steps:")
    print(f"1. Test with SteganoGAN demo:")
    print(f"   python scripts/demo.py --image {args.output_dir}/chest_xray/256x256/chest_xray_256x256_001.png")
    print(f"2. Use for training:")
    print(f"   python scripts/train.py --image_dir {args.output_dir}")
    print(f"3. Evaluate steganographic performance across different image types")


if __name__ == "__main__":
    main()

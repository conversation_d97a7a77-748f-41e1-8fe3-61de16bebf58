#!/usr/bin/env python3
"""
Debug script for dataset issues.
"""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    print("Testing imports...")
    
    import torch
    print("✓ PyTorch imported")
    
    from src.data.dataset import MedicalDataProcessor
    print("✓ MedicalDataProcessor imported")
    
    # Test medical data processor
    processor = MedicalDataProcessor(max_length=1024)
    print("✓ MedicalDataProcessor created")
    
    # Test with sample medical record
    sample_record = {
        'patient_id': 'TEST001',
        'symptoms': ['chest pain'],
        'diagnosis': 'hypertension',
        'lab_results': 'BP: 140/90, HR: 80',
        'medications': ['lisinopril'],
        'notes': 'Test patient'
    }
    
    encoded = processor.encode_medical_record(sample_record)
    print(f"✓ Medical record encoded, shape: {encoded.shape}")
    
    from src.data.dataset import SteganoDataset
    print("✓ SteganoDataset imported")
    
    # Test dataset creation
    dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_train_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,
        medical_data_ratio=1.0
    )
    print(f"✓ Dataset created, length: {len(dataset)}")
    
    if len(dataset) > 0:
        # Test getting a single sample
        sample = dataset[0]
        print(f"✓ Sample retrieved")
        print(f"  Image shape: {sample['image'].shape}")
        print(f"  Medical data shape: {sample['medical_data'].shape}")
        print(f"  Image path: {sample['image_path']}")
        
        # Test getting multiple samples to check consistency
        sample2 = dataset[1] if len(dataset) > 1 else dataset[0]
        print(f"✓ Second sample retrieved")
        print(f"  Medical data shape: {sample2['medical_data'].shape}")
        
        # Check if shapes are consistent
        if sample['medical_data'].shape == sample2['medical_data'].shape:
            print("✓ Medical data shapes are consistent")
        else:
            print(f"✗ Medical data shapes inconsistent: {sample['medical_data'].shape} vs {sample2['medical_data'].shape}")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

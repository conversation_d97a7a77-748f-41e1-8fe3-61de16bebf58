# SteganoGAN Medical Data Steganography - Quick Start Guide

This guide will help you get started with the SteganoGAN system for secure medical data transmission.

## Installation

### 1. Environment Setup

```bash
# Create virtual environment
python -m venv steganogan_env
source steganogan_env/bin/activate  # On Windows: steganogan_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install the package in development mode
pip install -e .
```

### 2. Verify Installation

```bash
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import torchvision; print(f'TorchVision version: {torchvision.__version__}')"
```

## Quick Demo

### 1. Prepare Sample Data

Create a sample cover image and medical data:

```bash
# Create directories
mkdir -p data/raw data/medical_samples results

# Download a sample image (or use your own)
# Place it in data/raw/sample_image.jpg
```

### 2. Create Sample Medical Data

Create `data/medical_samples/sample_record.json`:

```json
{
    "patient_id": "P12345",
    "symptoms": ["chest pain", "shortness of breath"],
    "diagnosis": "hypertension",
    "lab_results": {
        "blood_pressure": "140/90",
        "heart_rate": 85,
        "temperature": 98.6,
        "glucose": 120
    },
    "medications": ["lisinopril", "aspirin"],
    "notes": "Patient presents with elevated blood pressure and chest discomfort."
}
```

### 3. Run Demo

```bash
# Run the demonstration script
python scripts/demo.py \
    --image data/raw/sample_image.jpg \
    --medical_data data/medical_samples/sample_record.json \
    --output results/demo \
    --device cuda  # or cpu if no GPU
```

This will:
- Load the cover image
- Embed the medical data using SteganoGAN
- Extract and verify the hidden data
- Generate evaluation metrics
- Save visualization results

## Training Your Own Model

### 1. Prepare Training Data

```bash
# Organize your training images
data/
├── raw/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
└── medical_samples/
    ├── records.json
    └── ...
```

### 2. Configure Training

Edit `config/training_config.py` or create a custom configuration:

```python
# Example custom configuration
config = TrainingConfig()
config.num_epochs = 100
config.data.batch_size = 16
config.optimizer.generator_lr = 2e-4
```

### 3. Start Training

```bash
# Train with default configuration
python scripts/train.py --config default

# Train with custom configuration
python scripts/train.py --config production --model_config high_quality

# Resume from checkpoint
python scripts/train.py --resume models/checkpoint_epoch_50.pth
```

### 4. Monitor Training

```bash
# View tensorboard logs
tensorboard --logdir results/logs

# Check training progress
tail -f results/logs/training.log
```

## Model Configurations

### Available Configurations

1. **Default**: Balanced performance and speed
2. **Lightweight**: Faster training, lower quality
3. **High Quality**: Better results, slower training

### Custom Configuration

```python
from config.model_config import ModelConfig

# Create custom configuration
config = ModelConfig()
config.generator.hidden_channels = 128
config.generator.num_blocks = 8
config.image_size = (512, 512)
```

## Evaluation

### Basic Evaluation

```bash
python scripts/evaluate.py \
    --model models/best_checkpoint.pth \
    --test_data data/test \
    --output results/evaluation
```

### Metrics Explained

- **PSNR**: Peak Signal-to-Noise Ratio (higher is better)
- **SSIM**: Structural Similarity Index (closer to 1 is better)
- **Extraction Accuracy**: How accurately medical data is recovered
- **Bit Error Rate**: Error rate in extracted data (lower is better)

## Medical Data Format

### Supported Formats

1. **JSON**: Structured medical records
2. **Text**: Plain text medical notes
3. **CSV**: Tabular medical data

### Example Medical Record

```json
{
    "patient_id": "P12345",
    "timestamp": "2024-01-15T10:30:00Z",
    "symptoms": ["fever", "cough", "fatigue"],
    "vital_signs": {
        "temperature": 101.2,
        "blood_pressure": "120/80",
        "heart_rate": 88,
        "respiratory_rate": 16
    },
    "diagnosis": "viral infection",
    "treatment_plan": "rest, fluids, monitor symptoms",
    "medications": [
        {
            "name": "acetaminophen",
            "dosage": "500mg",
            "frequency": "every 6 hours"
        }
    ],
    "follow_up": "in 3 days if symptoms persist"
}
```

## Security Considerations

### Data Privacy

1. **Anonymization**: Remove patient identifiers before embedding
2. **Encryption**: Consider additional encryption for sensitive data
3. **Access Control**: Implement proper access controls for the system

### Steganographic Security

1. **Key Management**: Secure handling of extraction keys
2. **Cover Image Selection**: Use diverse, natural-looking cover images
3. **Capacity Limits**: Don't exceed recommended embedding capacity

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```bash
   # Reduce batch size
   python scripts/train.py --config lightweight
   ```

2. **Poor Image Quality**
   ```bash
   # Increase reconstruction weight
   # Edit config/training_config.py
   config.loss.reconstruction_weight = 20.0
   ```

3. **Low Extraction Accuracy**
   ```bash
   # Increase decoder weight
   config.loss.decoder_weight = 10.0
   ```

### Performance Optimization

1. **GPU Utilization**
   ```bash
   # Monitor GPU usage
   nvidia-smi -l 1
   ```

2. **Memory Optimization**
   ```python
   # Enable mixed precision training
   config.mixed_precision = True
   ```

3. **Data Loading**
   ```python
   # Increase number of workers
   config.data.num_workers = 8
   ```

## Next Steps

1. **Experiment with Configurations**: Try different model and training configurations
2. **Evaluate Robustness**: Test against various image processing attacks
3. **Integration**: Integrate with your telemedicine platform
4. **Security Analysis**: Conduct thorough security analysis
5. **Performance Optimization**: Optimize for your specific use case

## Support

For questions and issues:
1. Check the documentation in `docs/`
2. Review the code examples in `notebooks/`
3. Create an issue on the project repository
4. Contact the research team

## Citation

If you use this work in your research, please cite:

```bibtex
@misc{steganogan-medical-2024,
    title={SteganoGAN for Secure Medical Data Transmission},
    author={[Your Name]},
    year={2024},
    note={PhD Research Project}
}
```

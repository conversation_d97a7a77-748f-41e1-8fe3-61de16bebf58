{"version": "0.2.0", "configurations": [{"name": "Demo - SteganoGAN Medical Steganography", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/demo.py", "args": ["--image", "data/raw/test_image.jpg", "--medical_data", "data/medical_samples/sample_records.json", "--output", "results/demo_output"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src;${workspaceFolder}"}, "justMyCode": false}, {"name": "Test System", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/test_system.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src;${workspaceFolder}"}, "justMyCode": false}, {"name": "Simple Demo", "type": "python", "request": "launch", "program": "${workspaceFolder}/simple_demo.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Setup MIMIC", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/setup_mimic.py", "args": ["--mimic_path", "path/to/mimic-iv", "--image_dir", "data/raw", "--action", "info"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src;${workspaceFolder}"}, "justMyCode": false}, {"name": "Setup Datasets", "type": "python", "request": "launch", "program": "${workspaceFolder}/scripts/setup_datasets.py", "args": ["--action", "setup"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}/src;${workspaceFolder}"}, "justMyCode": false}]}
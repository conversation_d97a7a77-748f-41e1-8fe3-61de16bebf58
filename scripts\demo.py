#!/usr/bin/env python3
"""
Demonstration script for SteganoGAN medical data steganography.
Shows how to use the system for hiding and extracting medical data.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from PIL import Image
import matplotlib.pyplot as plt
import json

# Import project modules
from models.generator import SteganoGenerator
from models.discriminator import SteganoDiscriminator
from models.decoder import SteganoDecoder
from data.dataset import MedicalDataProcessor, SyntheticMedicalDataGenerator
from evaluation.metrics import ComprehensiveEvaluator
from config.model_config import get_model_config


def load_models(checkpoint_path: str, model_config, device):
    """Load trained models from checkpoint."""
    # Create models
    generator = SteganoGenerator(
        input_channels=model_config.generator.input_channels,
        output_channels=model_config.generator.output_channels,
        hidden_channels=model_config.generator.hidden_channels,
        num_blocks=model_config.generator.num_blocks,
        max_data_length=model_config.generator.max_data_length,
        embedding_dim=model_config.generator.embedding_dim,
        use_attention=model_config.generator.use_attention,
        norm_type=model_config.generator.norm_type,
        activation=model_config.generator.activation,
        dropout_rate=model_config.generator.dropout_rate
    ).to(device)

    decoder = SteganoDecoder(
        input_channels=model_config.decoder.input_channels,
        hidden_channels=model_config.decoder.hidden_channels,
        num_layers=model_config.decoder.num_layers,
        output_dim=model_config.decoder.output_dim,
        use_robust=True,
        use_attention=model_config.decoder.use_attention,
        norm_type=model_config.decoder.norm_type,
        activation=model_config.decoder.activation,
        dropout_rate=model_config.decoder.dropout_rate
    ).to(device)

    # Load checkpoint if provided
    if checkpoint_path and os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location=device)
        generator.load_state_dict(checkpoint['generator_state_dict'])
        decoder.load_state_dict(checkpoint['decoder_state_dict'])
        print(f"Loaded models from {checkpoint_path}")
    else:
        print("Using randomly initialized models (for demonstration)")

    generator.eval()
    decoder.eval()

    return generator, decoder


def load_and_preprocess_image(image_path: str, image_size=(256, 256)):
    """Load and preprocess an image."""
    transform = transforms.Compose([
        transforms.Resize(image_size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])  # [-1, 1]
    ])

    image = Image.open(image_path).convert('RGB')
    image_tensor = transform(image).unsqueeze(0)  # Add batch dimension

    return image_tensor, image


def denormalize_image(tensor):
    """Convert normalized tensor back to PIL Image."""
    # Denormalize from [-1, 1] to [0, 1]
    denorm = (tensor + 1) / 2
    denorm = torch.clamp(denorm, 0, 1)

    # Convert to PIL Image
    transform = transforms.ToPILImage()
    return transform(denorm.squeeze(0))


def demonstrate_steganography(
    generator, decoder, cover_image_tensor, medical_data_tensor,
    medical_record, data_processor, device
):
    """Demonstrate the steganographic process."""
    print("\n" + "="*60)
    print("STEGANOGRAPHIC DEMONSTRATION")
    print("="*60)

    with torch.no_grad():
        # Move data to device
        cover_image = cover_image_tensor.to(device)
        medical_data = medical_data_tensor.to(device)

        print(f"Cover image shape: {cover_image.shape}")
        print(f"Medical data shape: {medical_data.shape}")

        # Generate steganographic image
        print("\n1. Generating steganographic image...")
        stego_image, data_embedding = generator(cover_image, medical_data)

        print(f"Steganographic image shape: {stego_image.shape}")
        print(f"Data embedding shape: {data_embedding.shape}")

        # Extract hidden data
        print("\n2. Extracting hidden medical data...")
        extracted_data, confidence = decoder(stego_image)

        print(f"Extracted data shape: {extracted_data.shape}")
        print(f"Extraction confidence: {confidence.mean().item():.4f}")

        # Decode extracted data
        print("\n3. Decoding extracted medical data...")
        try:
            extracted_text = data_processor.decode_tensor(extracted_data[0])
            original_text = data_processor.decode_tensor(medical_data[0])

            print(f"Original medical data length: {len(original_text)} characters")
            print(f"Extracted medical data length: {len(extracted_text)} characters")

            # Try to parse as JSON
            try:
                extracted_record = json.loads(extracted_text)
                print("\nExtracted medical record:")
                print(json.dumps(extracted_record, indent=2))
            except json.JSONDecodeError:
                print(f"\nExtracted text (first 200 chars): {extracted_text[:200]}...")

        except Exception as e:
            print(f"Error decoding data: {e}")

        return stego_image, extracted_data, confidence


def evaluate_performance(
    cover_image, stego_image, original_data, extracted_data,
    confidence, data_processor
):
    """Evaluate the performance of the steganographic system."""
    print("\n" + "="*60)
    print("PERFORMANCE EVALUATION")
    print("="*60)

    evaluator = ComprehensiveEvaluator(data_processor)

    # Comprehensive evaluation
    metrics = evaluator.evaluate_batch(
        cover_image, stego_image, original_data, extracted_data, confidence
    )

    print("\nImage Quality Metrics:")
    print(f"  PSNR: {metrics['psnr']:.2f} dB")
    print(f"  SSIM: {metrics['ssim']:.4f}")
    print(f"  MSE: {metrics['mse']:.6f}")

    print("\nSteganographic Performance:")
    print(f"  Extraction Accuracy: {metrics['extraction_accuracy']:.4f}")
    print(f"  Bit Error Rate: {metrics['bit_error_rate']:.4f}")
    print(f"  Payload Recovery Rate: {metrics['payload_recovery_rate']:.4f}")

    print("\nMedical Data Integrity:")
    print(f"  Text Similarity: {metrics['text_similarity']:.4f}")
    print(f"  Average Confidence: {metrics['avg_confidence']:.4f}")

    return metrics


def visualize_results(cover_image_pil, stego_image_tensor, save_path=None):
    """Visualize the original and steganographic images."""
    print("\n" + "="*60)
    print("VISUALIZATION")
    print("="*60)

    # Convert steganographic image to PIL
    stego_image_pil = denormalize_image(stego_image_tensor.cpu())

    # Create comparison plot
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))

    axes[0].imshow(cover_image_pil)
    axes[0].set_title("Original Cover Image")
    axes[0].axis('off')

    axes[1].imshow(stego_image_pil)
    axes[1].set_title("Steganographic Image")
    axes[1].axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")

    plt.show()


def main():
    parser = argparse.ArgumentParser(description='SteganoGAN Medical Data Steganography Demo')
    parser.add_argument('--image', type=str, required=True, help='Path to cover image')
    parser.add_argument('--checkpoint', type=str, default=None, help='Path to model checkpoint')
    parser.add_argument('--config', type=str, default='default', help='Model configuration')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--output', type=str, default='demo_results', help='Output directory')
    parser.add_argument('--medical_data', type=str, default=None, help='Path to medical data JSON')

    args = parser.parse_args()

    # Setup
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create output directory
    output_dir = Path(args.output)
    output_dir.mkdir(exist_ok=True)

    # Load configuration
    model_config = get_model_config(args.config)

    # Load models
    print("Loading models...")
    generator, decoder = load_models(args.checkpoint, model_config, device)

    # Load and preprocess image
    print(f"Loading cover image: {args.image}")
    cover_image_tensor, cover_image_pil = load_and_preprocess_image(args.image)

    # Prepare medical data
    data_processor = MedicalDataProcessor(model_config.generator.max_data_length)

    if args.medical_data and os.path.exists(args.medical_data):
        # Load medical data from file
        with open(args.medical_data, 'r') as f:
            medical_data = json.load(f)

        # Handle both single record and list of records
        if isinstance(medical_data, list):
            medical_record = medical_data[0]  # Use first record
            print(f"Loaded medical data from: {args.medical_data} (using first record)")
        else:
            medical_record = medical_data
            print(f"Loaded medical data from: {args.medical_data}")
    else:
        # Generate synthetic medical data
        synthetic_generator = SyntheticMedicalDataGenerator()
        medical_record = synthetic_generator.generate_patient_record()
        print("Generated synthetic medical data")

    print(f"\nMedical Record:")
    print(json.dumps(medical_record, indent=2))

    # Encode medical data
    medical_data_tensor = data_processor.encode_medical_record(medical_record).unsqueeze(0)

    # Demonstrate steganography
    stego_image, extracted_data, confidence = demonstrate_steganography(
        generator, decoder, cover_image_tensor, medical_data_tensor,
        medical_record, data_processor, device
    )

    # Evaluate performance
    metrics = evaluate_performance(
        cover_image_tensor, stego_image, medical_data_tensor,
        extracted_data, confidence, data_processor
    )

    # Visualize results
    viz_path = output_dir / "comparison.png"
    visualize_results(cover_image_pil, stego_image, str(viz_path))

    # Save results
    results = {
        'medical_record': medical_record,
        'metrics': metrics,
        'config': {
            'image_path': args.image,
            'checkpoint': args.checkpoint,
            'model_config': args.config
        }
    }

    results_path = output_dir / "demo_results.json"
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\nDemo results saved to: {results_path}")
    print("\nDemo completed successfully!")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Quick test of training loop to verify it works.
"""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    print("Testing training loop...")
    
    import torch
    from src.data.dataset import SteganoDataset, create_dataloader
    from models.generator import SteganoGenerator
    from models.discriminator import SteganoDiscriminator
    from models.decoder import SteganoDecoder
    from training.losses import SteganoGANLoss
    from config.model_config import get_model_config
    
    # Setup device
    device = torch.device('cpu')
    print(f"Using device: {device}")
    
    # Load model config
    model_config = get_model_config('default')
    
    # Create models (simplified)
    generator = SteganoGenerator(
        input_channels=3,
        output_channels=3,
        hidden_channels=64,
        num_blocks=4,
        max_data_length=1024,
        embedding_dim=256
    ).to(device)
    
    discriminator = SteganoDiscriminator(
        input_channels=3,
        hidden_channels=64,
        num_layers=3
    ).to(device)
    
    decoder = SteganoDecoder(
        input_channels=3,
        hidden_channels=64,
        num_layers=3,
        output_dim=1024
    ).to(device)
    
    print("✓ Models created")
    
    # Create dataset and dataloader
    dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_train_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,
        medical_data_ratio=1.0
    )
    
    dataloader = create_dataloader(
        dataset,
        batch_size=2,  # Small batch size for quick test
        shuffle=False,
        num_workers=0,
        pin_memory=False
    )
    
    print(f"✓ Dataset and dataloader created, {len(dataset)} samples")
    
    # Create loss criterion
    criterion = SteganoGANLoss()
    print("✓ Loss criterion created")
    
    # Test one forward pass
    print("Testing forward pass...")
    
    for batch_idx, batch in enumerate(dataloader):
        print(f"Processing batch {batch_idx}...")
        
        # Move data to device
        real_images = batch['image'].to(device)
        medical_data = batch['medical_data'].to(device)
        
        print(f"  Real images shape: {real_images.shape}")
        print(f"  Medical data shape: {medical_data.shape}")
        
        # Test generator
        fake_images, data_embedding = generator(real_images, medical_data)
        print(f"  Generated images shape: {fake_images.shape}")
        
        # Test discriminator
        real_disc_outputs = discriminator(real_images)
        fake_disc_outputs = discriminator(fake_images)
        print(f"  Discriminator outputs: real={len(real_disc_outputs)}, fake={len(fake_disc_outputs)}")
        
        # Test decoder
        extracted_data, confidence = decoder(fake_images)
        print(f"  Extracted data shape: {extracted_data.shape}")
        print(f"  Confidence shape: {confidence.shape}")
        
        print("✓ Forward pass successful!")
        break  # Only test one batch
    
    print("Training loop test completed successfully!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

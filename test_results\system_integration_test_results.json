{"test_timestamp": "2025-05-26T06:37:35.189446", "configuration": {"synthetic_data_path": "data\\medical_samples\\compact_sample_records.json", "image_dir": "data\\medical_images", "mimic_path": null, "output_dir": "test_results"}, "tests": {"data_loading": {"status": "passed", "synthetic_data": {"loaded": true, "record_count": 20, "sample_record_size": 375, "average_size": 519.15}, "real_data": {}, "errors": []}, "image_loading": {"status": "passed", "image_types": {"chest_xray": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "ct_scan": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "ultrasound": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}, "mri": {"sizes": {"256x256": {"count": 25, "sample_size": [256, 256], "sample_mode": "RGB"}}, "total_images": 25}}, "errors": []}, "data_preprocessing": {"status": "failed", "medical_processor": {}, "image_transforms": {"available": true, "transform_successful": true, "output_shape": [3, 256, 256]}, "errors": ["Medical processor test failed: MedicalDataProcessor.__init__() got an unexpected keyword argument 'max_data_length'"]}, "steganography_simulation": {"status": "passed", "embedding_test": {"successful": true, "data_size": 99, "capacity_sufficient": true, "utilization_percent": 0.40283203125}, "extraction_test": {"successful": true, "data_integrity": true}, "quality_metrics": {"mse": 0.0018564860026041667, "psnr": 75.44388681754798, "quality_acceptable": "True"}, "errors": []}, "end_to_end_pipeline": {"status": "passed", "pipeline_steps": {"data_loading": {"successful": true}, "image_loading": {"successful": true}, "data_encoding": {"successful": true, "encoded_size": 331}, "steganographic_embedding": {"successful": true}, "data_extraction": {"successful": true}}, "performance_metrics": {"total_time_seconds": 0.015795230865478516, "steps_completed": 5}, "errors": []}}, "summary": {"total_tests": 5, "passed_tests": 4, "failed_tests": 1, "success_rate": 0.8, "overall_status": "failed"}}
#!/usr/bin/env python3
"""
Enhanced training script for SteganoGAN medical data steganography.
Includes comprehensive monitoring, checkpointing, and recovery capabilities.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime
import json
import shutil
import csv
import time
import glob
from typing import Dict, Any, Tuple, Optional, List
from collections import defaultdict, deque

# Add project root and src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np

# Import tensorboard conditionally to avoid errors
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available. Install with: pip install tensorboard")
    TENSORBOARD_AVAILABLE = False
    SummaryWriter = None

from tqdm import tqdm

# Import project modules
from models.generator import SteganoGenerator
from models.discriminator import SteganoDiscriminator
from models.decoder import SteganoDecoder
from training.losses import SteganoGA<PERSON>oss
from data.dataset import SteganoDataset, create_dataloader
from config.model_config import get_model_config
from config.training_config import get_training_config


class TrainingMonitor:
    """Comprehensive training monitoring and logging system."""

    def __init__(self, log_dir: str, use_tensorboard: bool = True):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # Initialize logging
        self.logger = self._setup_logging()

        # TensorBoard writer
        self.writer = None
        if use_tensorboard and TENSORBOARD_AVAILABLE:
            self.writer = SummaryWriter(str(self.log_dir))
            self.logger.info(f"TensorBoard logging enabled: {self.log_dir}")

        # Loss history tracking
        self.loss_history = defaultdict(list)
        self.metrics_history = defaultdict(list)

        # Running averages for smooth display
        self.running_averages = defaultdict(lambda: deque(maxlen=100))

        # Training statistics
        self.start_time = time.time()
        self.epoch_times = []
        self.best_metrics = {}

        # CSV logging
        self.csv_file = self.log_dir / "training_metrics.csv"
        self.csv_headers = [
            'epoch', 'batch', 'train_gen_loss', 'train_disc_loss', 'train_data_confidence',
            'val_gen_loss', 'val_disc_loss', 'val_data_confidence', 'learning_rate_gen',
            'learning_rate_disc', 'epoch_time', 'total_time'
        ]
        self._init_csv()

    def _setup_logging(self) -> logging.Logger:
        """Setup comprehensive logging."""
        logger = logging.getLogger('SteganoGAN')
        logger.setLevel(logging.INFO)

        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # File handler
        file_handler = logging.FileHandler(self.log_dir / 'training.log')
        file_handler.setLevel(logging.INFO)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def _init_csv(self):
        """Initialize CSV logging file."""
        with open(self.csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(self.csv_headers)

    def log_batch(self, epoch: int, batch_idx: int, metrics: Dict[str, float],
                  global_step: int, learning_rates: Dict[str, float] = None):
        """Log batch-level metrics."""
        # Update running averages
        for key, value in metrics.items():
            self.running_averages[key].append(value)

        # TensorBoard logging
        if self.writer is not None:
            for key, value in metrics.items():
                self.writer.add_scalar(f'Batch/{key}', value, global_step)

            if learning_rates:
                for key, lr in learning_rates.items():
                    self.writer.add_scalar(f'LearningRate/{key}', lr, global_step)

    def log_epoch(self, epoch: int, train_metrics: Dict[str, float],
                  val_metrics: Dict[str, float] = None, learning_rates: Dict[str, float] = None):
        """Log epoch-level metrics."""
        epoch_time = time.time()
        if self.epoch_times:
            epoch_duration = epoch_time - self.epoch_times[-1]
        else:
            epoch_duration = epoch_time - self.start_time

        self.epoch_times.append(epoch_time)
        total_time = epoch_time - self.start_time

        # Store metrics
        for key, value in train_metrics.items():
            self.loss_history[f'train_{key}'].append(value)

        if val_metrics:
            for key, value in val_metrics.items():
                self.loss_history[f'val_{key}'].append(value)

        # TensorBoard logging
        if self.writer is not None:
            for key, value in train_metrics.items():
                self.writer.add_scalar(f'Epoch/Train_{key}', value, epoch)

            if val_metrics:
                for key, value in val_metrics.items():
                    self.writer.add_scalar(f'Epoch/Val_{key}', value, epoch)

            if learning_rates:
                for key, lr in learning_rates.items():
                    self.writer.add_scalar(f'Epoch/LR_{key}', lr, epoch)

            self.writer.add_scalar('Epoch/Duration', epoch_duration, epoch)

        # CSV logging
        csv_row = [
            epoch, -1,  # -1 for batch indicates epoch-level metrics
            train_metrics.get('gen_loss', 0),
            train_metrics.get('disc_loss', 0),
            train_metrics.get('data_confidence', 0),
            val_metrics.get('gen_loss', 0) if val_metrics else 0,
            val_metrics.get('disc_loss', 0) if val_metrics else 0,
            val_metrics.get('data_confidence', 0) if val_metrics else 0,
            learning_rates.get('generator', 0) if learning_rates else 0,
            learning_rates.get('discriminator', 0) if learning_rates else 0,
            epoch_duration,
            total_time
        ]

        with open(self.csv_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(csv_row)

        # Console logging with running averages
        train_gen_avg = np.mean(list(self.running_averages['gen_loss']))
        train_disc_avg = np.mean(list(self.running_averages['disc_loss']))

        log_msg = f"Epoch {epoch:3d} | "
        log_msg += f"Gen: {train_metrics.get('gen_loss', 0):.4f} (avg: {train_gen_avg:.4f}) | "
        log_msg += f"Disc: {train_metrics.get('disc_loss', 0):.4f} (avg: {train_disc_avg:.4f}) | "

        if val_metrics:
            log_msg += f"Val Gen: {val_metrics.get('gen_loss', 0):.4f} | "
            log_msg += f"Val Disc: {val_metrics.get('disc_loss', 0):.4f} | "

        log_msg += f"Time: {epoch_duration:.1f}s"

        self.logger.info(log_msg)

    def log_best_model(self, epoch: int, metric_name: str, metric_value: float):
        """Log when a new best model is found."""
        self.best_metrics[metric_name] = {
            'epoch': epoch,
            'value': metric_value,
            'timestamp': datetime.now().isoformat()
        }

        self.logger.info(f"*** New best {metric_name}: {metric_value:.4f} at epoch {epoch}")

        if self.writer is not None:
            self.writer.add_scalar(f'Best/{metric_name}', metric_value, epoch)

    def save_loss_history(self):
        """Save complete loss history to JSON."""
        history_file = self.log_dir / "loss_history.json"

        # Convert deques to lists for JSON serialization
        history_dict = {}
        for key, values in self.loss_history.items():
            history_dict[key] = list(values)

        with open(history_file, 'w') as f:
            json.dump({
                'loss_history': history_dict,
                'best_metrics': self.best_metrics,
                'training_duration': time.time() - self.start_time,
                'total_epochs': len(self.epoch_times)
            }, f, indent=2)

    def get_running_average(self, metric_name: str, window: int = 10) -> float:
        """Get running average of a metric."""
        if metric_name not in self.running_averages:
            return 0.0

        values = list(self.running_averages[metric_name])
        if not values:
            return 0.0

        return np.mean(values[-window:])

    def close(self):
        """Close monitoring resources."""
        if self.writer is not None:
            self.writer.close()

        self.save_loss_history()
        self.logger.info(f"Training monitoring completed. Total time: {time.time() - self.start_time:.1f}s")

        # Close all logger handlers to release file locks
        for handler in self.logger.handlers[:]:
            handler.close()
            self.logger.removeHandler(handler)


class CheckpointManager:
    """Enhanced checkpoint management with automatic cleanup and recovery."""

    def __init__(self, checkpoint_dir: str, max_checkpoints: int = 5, logger: logging.Logger = None):
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.max_checkpoints = max_checkpoints
        self.logger = logger or logging.getLogger(__name__)

        # Track checkpoint files
        self.checkpoint_pattern = "checkpoint_epoch_*.pth"
        self.best_checkpoint_name = "best_checkpoint.pth"
        self.latest_checkpoint_name = "latest_checkpoint.pth"

    def save_checkpoint(self,
                       generator, discriminator, decoder,
                       gen_optimizer, disc_optimizer, dec_optimizer,
                       schedulers: List,
                       epoch: int,
                       train_metrics: Dict[str, float],
                       val_metrics: Dict[str, float] = None,
                       is_best: bool = False,
                       additional_info: Dict = None):
        """Save comprehensive checkpoint with all training state."""

        # Prepare checkpoint data
        checkpoint = {
            'epoch': epoch,
            'timestamp': datetime.now().isoformat(),

            # Model states
            'generator_state_dict': generator.state_dict(),
            'discriminator_state_dict': discriminator.state_dict(),
            'decoder_state_dict': decoder.state_dict(),

            # Optimizer states
            'gen_optimizer_state_dict': gen_optimizer.state_dict(),
            'disc_optimizer_state_dict': disc_optimizer.state_dict(),
            'dec_optimizer_state_dict': dec_optimizer.state_dict(),

            # Scheduler states
            'scheduler_states': [s.state_dict() if s is not None else None for s in schedulers],

            # Training metrics
            'train_metrics': train_metrics,
            'val_metrics': val_metrics or {},

            # Additional information
            'additional_info': additional_info or {}
        }

        # Save regular checkpoint
        checkpoint_path = self.checkpoint_dir / f'checkpoint_epoch_{epoch:04d}.pth'
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")

        # Save as latest checkpoint
        latest_path = self.checkpoint_dir / self.latest_checkpoint_name
        torch.save(checkpoint, latest_path)

        # Save best checkpoint if applicable
        if is_best:
            best_path = self.checkpoint_dir / self.best_checkpoint_name
            torch.save(checkpoint, best_path)
            self.logger.info(f"🏆 Best checkpoint saved: {best_path}")

        # Cleanup old checkpoints
        self._cleanup_old_checkpoints()

        return checkpoint_path

    def load_checkpoint(self, checkpoint_path: str = None) -> Dict:
        """Load checkpoint from file."""
        if checkpoint_path is None:
            # Try to load latest checkpoint
            latest_path = self.checkpoint_dir / self.latest_checkpoint_name
            if latest_path.exists():
                checkpoint_path = latest_path
            else:
                # Find most recent checkpoint
                checkpoints = list(self.checkpoint_dir.glob(self.checkpoint_pattern))
                if not checkpoints:
                    raise FileNotFoundError("No checkpoints found")
                checkpoint_path = max(checkpoints, key=lambda p: p.stat().st_mtime)

        checkpoint_path = Path(checkpoint_path)
        if not checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")

        self.logger.info(f"Loading checkpoint: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        return checkpoint

    def restore_training_state(self,
                              checkpoint: Dict,
                              generator, discriminator, decoder,
                              gen_optimizer, disc_optimizer, dec_optimizer,
                              schedulers: List) -> int:
        """Restore complete training state from checkpoint."""

        # Load model states
        generator.load_state_dict(checkpoint['generator_state_dict'])
        discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        decoder.load_state_dict(checkpoint['decoder_state_dict'])

        # Load optimizer states
        gen_optimizer.load_state_dict(checkpoint['gen_optimizer_state_dict'])
        disc_optimizer.load_state_dict(checkpoint['disc_optimizer_state_dict'])
        dec_optimizer.load_state_dict(checkpoint['dec_optimizer_state_dict'])

        # Load scheduler states
        scheduler_states = checkpoint.get('scheduler_states', [])
        for scheduler, state in zip(schedulers, scheduler_states):
            if scheduler is not None and state is not None:
                scheduler.load_state_dict(state)

        epoch = checkpoint['epoch']
        self.logger.info(f"Training state restored from epoch {epoch}")

        return epoch

    def _cleanup_old_checkpoints(self):
        """Remove old checkpoint files, keeping only the most recent ones."""
        checkpoints = list(self.checkpoint_dir.glob(self.checkpoint_pattern))

        if len(checkpoints) <= self.max_checkpoints:
            return

        # Sort by modification time (newest first)
        checkpoints.sort(key=lambda p: p.stat().st_mtime, reverse=True)

        # Remove old checkpoints
        for checkpoint in checkpoints[self.max_checkpoints:]:
            try:
                checkpoint.unlink()
                self.logger.debug(f"Removed old checkpoint: {checkpoint}")
            except Exception as e:
                self.logger.warning(f"Failed to remove checkpoint {checkpoint}: {e}")

    def list_checkpoints(self) -> List[Dict]:
        """List all available checkpoints with metadata."""
        checkpoints = []

        for checkpoint_file in self.checkpoint_dir.glob(self.checkpoint_pattern):
            try:
                # Load checkpoint metadata
                checkpoint = torch.load(checkpoint_file, map_location='cpu')

                checkpoints.append({
                    'file': str(checkpoint_file),
                    'epoch': checkpoint.get('epoch', 0),
                    'timestamp': checkpoint.get('timestamp', ''),
                    'train_metrics': checkpoint.get('train_metrics', {}),
                    'val_metrics': checkpoint.get('val_metrics', {}),
                    'file_size': checkpoint_file.stat().st_size
                })
            except Exception as e:
                self.logger.warning(f"Failed to read checkpoint {checkpoint_file}: {e}")

        # Sort by epoch
        checkpoints.sort(key=lambda x: x['epoch'])

        return checkpoints

    def get_best_checkpoint_path(self) -> Optional[Path]:
        """Get path to best checkpoint if it exists."""
        best_path = self.checkpoint_dir / self.best_checkpoint_name
        return best_path if best_path.exists() else None

    def get_latest_checkpoint_path(self) -> Optional[Path]:
        """Get path to latest checkpoint if it exists."""
        latest_path = self.checkpoint_dir / self.latest_checkpoint_name
        return latest_path if latest_path.exists() else None


def setup_logging(log_dir: str) -> logging.Logger:
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)


def create_models(model_config, device):
    """Create and initialize models."""
    # Generator
    generator = SteganoGenerator(
        input_channels=model_config.generator.input_channels,
        output_channels=model_config.generator.output_channels,
        hidden_channels=model_config.generator.hidden_channels,
        num_blocks=model_config.generator.num_blocks,
        max_data_length=model_config.generator.max_data_length,
        embedding_dim=model_config.generator.embedding_dim,
        use_attention=model_config.generator.use_attention,
        norm_type=model_config.generator.norm_type,
        activation=model_config.generator.activation,
        dropout_rate=model_config.generator.dropout_rate
    ).to(device)

    # Discriminator
    discriminator = SteganoDiscriminator(
        input_channels=model_config.discriminator.input_channels,
        hidden_channels=model_config.discriminator.hidden_channels,
        num_layers=model_config.discriminator.num_layers,
        patch_size=model_config.discriminator.patch_size,
        use_multiscale=True,
        num_scales=2,
        norm_type=model_config.discriminator.norm_type,
        activation=model_config.discriminator.activation,
        use_spectral_norm=model_config.discriminator.use_spectral_norm,
        dropout_rate=model_config.discriminator.dropout_rate
    ).to(device)

    # Decoder
    decoder = SteganoDecoder(
        input_channels=model_config.decoder.input_channels,
        hidden_channels=model_config.decoder.hidden_channels,
        num_layers=model_config.decoder.num_layers,
        output_dim=model_config.decoder.output_dim,
        use_robust=True,
        use_attention=model_config.decoder.use_attention,
        norm_type=model_config.decoder.norm_type,
        activation=model_config.decoder.activation,
        dropout_rate=model_config.decoder.dropout_rate
    ).to(device)

    return generator, discriminator, decoder


def create_optimizers(generator, discriminator, decoder, training_config):
    """Create optimizers for all models."""
    # Generator optimizer
    gen_optimizer = optim.Adam(
        generator.parameters(),
        lr=training_config.optimizer.generator_lr,
        betas=training_config.optimizer.generator_betas,
        weight_decay=training_config.optimizer.generator_weight_decay
    )

    # Discriminator optimizer
    disc_optimizer = optim.Adam(
        discriminator.parameters(),
        lr=training_config.optimizer.discriminator_lr,
        betas=training_config.optimizer.discriminator_betas,
        weight_decay=training_config.optimizer.discriminator_weight_decay
    )

    # Decoder optimizer
    dec_optimizer = optim.Adam(
        decoder.parameters(),
        lr=training_config.optimizer.decoder_lr,
        betas=training_config.optimizer.decoder_betas,
        weight_decay=training_config.optimizer.decoder_weight_decay
    )

    return gen_optimizer, disc_optimizer, dec_optimizer


def create_schedulers(optimizers, training_config):
    """Create learning rate schedulers."""
    schedulers = []

    if training_config.optimizer.use_scheduler:
        for optimizer in optimizers:
            if training_config.optimizer.scheduler_type == "cosine":
                scheduler = optim.lr_scheduler.CosineAnnealingLR(
                    optimizer, T_max=training_config.num_epochs
                )
            elif training_config.optimizer.scheduler_type == "step":
                scheduler = optim.lr_scheduler.StepLR(
                    optimizer, step_size=50, gamma=training_config.optimizer.scheduler_factor
                )
            elif training_config.optimizer.scheduler_type == "plateau":
                scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                    optimizer, mode='min',
                    patience=training_config.optimizer.scheduler_patience,
                    factor=training_config.optimizer.scheduler_factor
                )
            else:
                scheduler = None

            schedulers.append(scheduler)
    else:
        schedulers = [None, None, None]

    return schedulers


def train_epoch(
    generator, discriminator, decoder,
    gen_optimizer, disc_optimizer, dec_optimizer,
    dataloader, criterion, device, epoch, monitor: TrainingMonitor
):
    """Enhanced train epoch with comprehensive monitoring."""
    generator.train()
    discriminator.train()
    decoder.train()

    total_gen_loss = 0.0
    total_disc_loss = 0.0
    total_confidence = 0.0
    num_batches = len(dataloader)

    progress_bar = tqdm(dataloader, desc=f"Epoch {epoch}")

    for batch_idx, batch in enumerate(progress_bar):
        # Move data to device
        real_images = batch['image'].to(device)
        medical_data = batch['medical_data'].to(device)

        # ==================
        # Train Discriminator
        # ==================
        disc_optimizer.zero_grad()

        # Generate fake images
        with torch.no_grad():
            fake_images, _ = generator(real_images, medical_data)

        # Discriminator outputs
        real_disc_outputs = discriminator(real_images)
        fake_disc_outputs = discriminator(fake_images.detach())

        # Discriminator loss
        disc_loss, _ = criterion.discriminator_loss(real_disc_outputs, fake_disc_outputs)
        disc_loss.backward()
        disc_optimizer.step()

        # ==================
        # Train Generator and Decoder
        # ==================
        gen_optimizer.zero_grad()
        dec_optimizer.zero_grad()

        # Generate fake images
        fake_images, _ = generator(real_images, medical_data)

        # Discriminator outputs and features
        fake_disc_outputs = discriminator(fake_images)
        real_features = discriminator.get_features(real_images)
        fake_features = discriminator.get_features(fake_images)

        # Extract data from fake images
        extracted_data, confidence = decoder(fake_images)

        # Generator loss
        gen_loss, gen_losses = criterion.generator_loss(
            real_images, fake_images, fake_disc_outputs,
            real_features, fake_features,
            extracted_data, medical_data, confidence
        )

        gen_loss.backward()
        gen_optimizer.step()
        dec_optimizer.step()

        # Update running losses
        total_gen_loss += gen_loss.item()
        total_disc_loss += disc_loss.item()
        total_confidence += torch.mean(confidence).item()

        # Enhanced progress display
        current_gen_avg = monitor.get_running_average('gen_loss')
        current_disc_avg = monitor.get_running_average('disc_loss')

        progress_bar.set_postfix({
            'Gen': f"{gen_loss.item():.4f}",
            'Disc': f"{disc_loss.item():.4f}",
            'Conf': f"{torch.mean(confidence).item():.3f}",
            'G_avg': f"{current_gen_avg:.4f}",
            'D_avg': f"{current_disc_avg:.4f}"
        })

        # Batch-level logging
        if batch_idx % 10 == 0:  # Log more frequently
            global_step = epoch * len(dataloader) + batch_idx

            # Prepare batch metrics
            batch_metrics = {
                'gen_loss': gen_loss.item(),
                'disc_loss': disc_loss.item(),
                'data_confidence': torch.mean(confidence).item()
            }

            # Add individual loss components
            for loss_name, loss_value in gen_losses.items():
                batch_metrics[f'gen_{loss_name}'] = loss_value.item()

            # Get current learning rates
            learning_rates = {
                'generator': gen_optimizer.param_groups[0]['lr'],
                'discriminator': disc_optimizer.param_groups[0]['lr'],
                'decoder': dec_optimizer.param_groups[0]['lr']
            }

            monitor.log_batch(epoch, batch_idx, batch_metrics, global_step, learning_rates)

    # Calculate epoch averages
    avg_gen_loss = total_gen_loss / num_batches
    avg_disc_loss = total_disc_loss / num_batches
    avg_confidence = total_confidence / num_batches

    return {
        'gen_loss': avg_gen_loss,
        'disc_loss': avg_disc_loss,
        'data_confidence': avg_confidence
    }


def save_checkpoint(
    generator, discriminator, decoder,
    gen_optimizer, disc_optimizer, dec_optimizer,
    epoch, checkpoint_dir, is_best=False
):
    """Save model checkpoint."""
    os.makedirs(checkpoint_dir, exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'generator_state_dict': generator.state_dict(),
        'discriminator_state_dict': discriminator.state_dict(),
        'decoder_state_dict': decoder.state_dict(),
        'gen_optimizer_state_dict': gen_optimizer.state_dict(),
        'disc_optimizer_state_dict': disc_optimizer.state_dict(),
        'dec_optimizer_state_dict': dec_optimizer.state_dict(),
    }

    # Save regular checkpoint
    checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
    torch.save(checkpoint, checkpoint_path)

    # Save best checkpoint
    if is_best:
        best_path = os.path.join(checkpoint_dir, 'best_checkpoint.pth')
        torch.save(checkpoint, best_path)


def validate_epoch(
    generator, discriminator, decoder,
    dataloader, criterion, device, epoch, monitor: TrainingMonitor
):
    """Enhanced validation epoch with comprehensive monitoring."""
    generator.eval()
    discriminator.eval()
    decoder.eval()

    total_gen_loss = 0.0
    total_disc_loss = 0.0
    total_confidence = 0.0
    num_batches = len(dataloader)

    with torch.no_grad():
        for batch_idx, batch in enumerate(dataloader):
            # Move data to device
            real_images = batch['image'].to(device)
            medical_data = batch['medical_data'].to(device)

            # Generate fake images
            fake_images, _ = generator(real_images, medical_data)

            # Discriminator outputs
            real_disc_outputs = discriminator(real_images)
            fake_disc_outputs = discriminator(fake_images)

            # Extract data from fake images
            extracted_data, confidence = decoder(fake_images)

            # Calculate losses
            disc_loss, _ = criterion.discriminator_loss(real_disc_outputs, fake_disc_outputs)

            real_features = discriminator.get_features(real_images)
            fake_features = discriminator.get_features(fake_images)

            gen_loss, _ = criterion.generator_loss(
                real_images, fake_images, fake_disc_outputs,
                real_features, fake_features,
                extracted_data, medical_data, confidence
            )

            total_gen_loss += gen_loss.item()
            total_disc_loss += disc_loss.item()
            total_confidence += torch.mean(confidence).item()

    # Calculate averages
    avg_gen_loss = total_gen_loss / num_batches
    avg_disc_loss = total_disc_loss / num_batches
    avg_confidence = total_confidence / num_batches

    monitor.logger.info(f"Validation Epoch {epoch}: Gen Loss: {avg_gen_loss:.4f}, Disc Loss: {avg_disc_loss:.4f}")

    return {
        'gen_loss': avg_gen_loss,
        'disc_loss': avg_disc_loss,
        'data_confidence': avg_confidence
    }


def create_unified_dataloaders(training_config):
    """Create dataloaders for the unified medical dataset."""
    from torchvision import transforms
    from pathlib import Path
    import json

    # Define transforms
    train_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.RandomHorizontalFlip(p=0.5) if training_config.data.use_augmentation else transforms.Lambda(lambda x: x),
        transforms.ColorJitter(
            brightness=training_config.data.brightness_range,
            contrast=training_config.data.contrast_range
        ) if training_config.data.use_augmentation else transforms.Lambda(lambda x: x),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
    ])

    # Create datasets
    train_dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_train_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,  # Use real unified data
        medical_data_ratio=1.0,  # Use medical data for all samples
        transform=train_transform
    )

    val_dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_val_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,
        medical_data_ratio=1.0,
        transform=val_transform
    )

    # Create dataloaders with reduced workers to avoid multiprocessing issues
    train_dataloader = create_dataloader(
        train_dataset,
        batch_size=training_config.data.batch_size,
        shuffle=True,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        pin_memory=False  # Disable pin_memory when using CPU
    )

    val_dataloader = create_dataloader(
        val_dataset,
        batch_size=training_config.data.batch_size,
        shuffle=False,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        pin_memory=False  # Disable pin_memory when using CPU
    )

    return train_dataloader, val_dataloader


def main():
    parser = argparse.ArgumentParser(description='Enhanced SteganoGAN training with comprehensive monitoring')
    parser.add_argument('--config', type=str, default='default', help='Configuration name')
    parser.add_argument('--model_config', type=str, default='default', help='Model configuration name')
    parser.add_argument('--resume', type=str, default=None, help='Resume from checkpoint path')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--log_dir', type=str, default=None, help='Override log directory')
    parser.add_argument('--checkpoint_dir', type=str, default=None, help='Override checkpoint directory')

    args = parser.parse_args()

    # Load configurations
    model_config = get_model_config(args.model_config)
    training_config = get_training_config(args.config)

    # Override directories if provided
    if args.log_dir:
        training_config.log_dir = args.log_dir
    if args.checkpoint_dir:
        training_config.checkpoint_dir = args.checkpoint_dir

    # Setup device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create enhanced monitoring system
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path(training_config.log_dir) / f"training_{timestamp}"
    monitor = TrainingMonitor(str(log_dir), use_tensorboard=TENSORBOARD_AVAILABLE)

    # Create checkpoint manager
    checkpoint_dir = Path(training_config.checkpoint_dir) / f"checkpoints_{timestamp}"
    checkpoint_manager = CheckpointManager(str(checkpoint_dir), max_checkpoints=5, logger=monitor.logger)

    monitor.logger.info("*** Starting Enhanced SteganoGAN Training")
    monitor.logger.info(f"Log directory: {log_dir}")
    monitor.logger.info(f"Checkpoint directory: {checkpoint_dir}")

    # Create models
    generator, discriminator, decoder = create_models(model_config, device)
    monitor.logger.info("*** Models created successfully")

    # Create optimizers
    gen_optimizer, disc_optimizer, dec_optimizer = create_optimizers(
        generator, discriminator, decoder, training_config
    )

    # Create schedulers
    schedulers = create_schedulers([gen_optimizer, disc_optimizer, dec_optimizer], training_config)

    # Create loss criterion
    perceptual_layers = training_config.loss.perceptual_layers or ['relu1_2', 'relu2_2', 'relu3_3', 'relu4_3']
    criterion = SteganoGANLoss(
        adversarial_weight=training_config.loss.adversarial_weight,
        reconstruction_weight=training_config.loss.reconstruction_weight,
        perceptual_weight=training_config.loss.perceptual_weight,
        decoder_weight=training_config.loss.decoder_weight,
        feature_matching_weight=training_config.loss.feature_matching_weight,
        medical_data_weight=training_config.loss.medical_data_weight,
        perceptual_layers=perceptual_layers,
        use_feature_matching=training_config.loss.use_feature_matching
    )

    # Create dataset and dataloaders
    monitor.logger.info("Creating datasets and dataloaders...")
    train_dataloader, val_dataloader = create_unified_dataloaders(training_config)
    monitor.logger.info(f"Training samples: {len(train_dataloader.dataset)}")
    monitor.logger.info(f"Validation samples: {len(val_dataloader.dataset)}")

    # Resume from checkpoint if specified
    start_epoch = 1
    best_val_loss = float('inf')

    if args.resume:
        try:
            checkpoint = checkpoint_manager.load_checkpoint(args.resume)
            start_epoch = checkpoint_manager.restore_training_state(
                checkpoint, generator, discriminator, decoder,
                gen_optimizer, disc_optimizer, dec_optimizer, schedulers
            ) + 1

            # Restore best validation loss if available
            if 'val_metrics' in checkpoint and 'gen_loss' in checkpoint['val_metrics']:
                best_val_loss = checkpoint['val_metrics']['gen_loss']

            monitor.logger.info(f"*** Resumed training from epoch {start_epoch-1}")
            monitor.logger.info(f"Previous best validation loss: {best_val_loss:.4f}")

        except Exception as e:
            monitor.logger.error(f"Failed to resume from checkpoint: {e}")
            monitor.logger.info("Starting training from scratch")

    # Enhanced training loop
    monitor.logger.info("*** Starting enhanced training loop...")

    try:
        for epoch in range(start_epoch, training_config.num_epochs + 1):
            # Train epoch with enhanced monitoring
            train_metrics = train_epoch(
                generator, discriminator, decoder,
                gen_optimizer, disc_optimizer, dec_optimizer,
                train_dataloader, criterion, device, epoch, monitor
            )

            # Validation
            val_metrics = None
            if epoch % training_config.validate_every == 0:
                val_metrics = validate_epoch(
                    generator, discriminator, decoder,
                    val_dataloader, criterion, device, epoch, monitor
                )

                # Check if best model
                current_val_loss = val_metrics['gen_loss']
                is_best = current_val_loss < best_val_loss
                if is_best:
                    best_val_loss = current_val_loss
                    monitor.log_best_model(epoch, 'val_gen_loss', current_val_loss)
            else:
                is_best = False

            # Get current learning rates
            learning_rates = {
                'generator': gen_optimizer.param_groups[0]['lr'],
                'discriminator': disc_optimizer.param_groups[0]['lr'],
                'decoder': dec_optimizer.param_groups[0]['lr']
            }

            # Log epoch metrics
            monitor.log_epoch(epoch, train_metrics, val_metrics, learning_rates)

            # Save checkpoint
            if epoch % training_config.save_every == 0 or is_best:
                checkpoint_manager.save_checkpoint(
                    generator, discriminator, decoder,
                    gen_optimizer, disc_optimizer, dec_optimizer,
                    schedulers, epoch, train_metrics, val_metrics, is_best,
                    additional_info={'best_val_loss': best_val_loss}
                )

            # Update learning rate schedulers
            for scheduler in schedulers:
                if scheduler is not None:
                    if training_config.optimizer.scheduler_type == "plateau":
                        metric_for_scheduler = val_metrics['gen_loss'] if val_metrics else train_metrics['gen_loss']
                        scheduler.step(metric_for_scheduler)
                    else:
                        scheduler.step()

    except KeyboardInterrupt:
        monitor.logger.info("*** Training interrupted by user")
        # Save emergency checkpoint
        checkpoint_manager.save_checkpoint(
            generator, discriminator, decoder,
            gen_optimizer, disc_optimizer, dec_optimizer,
            schedulers, epoch, train_metrics, val_metrics, False,
            additional_info={'interrupted': True, 'best_val_loss': best_val_loss}
        )
        monitor.logger.info("*** Emergency checkpoint saved")

    except Exception as e:
        monitor.logger.error(f"*** Training failed with error: {e}")
        import traceback
        monitor.logger.error(traceback.format_exc())
        raise

    finally:
        # Cleanup and final logging
        monitor.logger.info("*** Training completed!")
        monitor.logger.info(f"Best validation loss achieved: {best_val_loss:.4f}")

        # List final checkpoints
        checkpoints = checkpoint_manager.list_checkpoints()
        monitor.logger.info(f"*** Saved {len(checkpoints)} checkpoints")

        # Close monitoring
        monitor.close()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Debug script for batch collation issues.
"""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    print("Testing batch collation...")
    
    import torch
    from src.data.dataset import SteganoDataset
    
    # Test dataset creation
    dataset = SteganoDataset(
        image_dir="data/medical_images",
        medical_data_path="data/unified_medical_real/unified_real_train_records.json",
        image_size=(256, 256),
        max_data_length=1024,
        use_synthetic_data=False,
        medical_data_ratio=1.0
    )
    print(f"✓ Dataset created, length: {len(dataset)}")
    
    # Get multiple samples and check their structure
    samples = []
    for i in range(4):
        sample = dataset[i]
        samples.append(sample)
        print(f"Sample {i}:")
        print(f"  Image shape: {sample['image'].shape}")
        print(f"  Medical data shape: {sample['medical_data'].shape}")
        print(f"  Medical record keys: {list(sample['medical_record'].keys())}")
        print(f"  Medical record types: {[(k, type(v)) for k, v in sample['medical_record'].items()]}")
        
        # Check if lists have different lengths
        for key, value in sample['medical_record'].items():
            if isinstance(value, list):
                print(f"    {key} length: {len(value)}")
    
    print("\nTesting manual batch creation...")
    
    # Try to manually create a batch to see what fails
    try:
        images = torch.stack([s['image'] for s in samples])
        print(f"✓ Images stacked: {images.shape}")
    except Exception as e:
        print(f"✗ Images stacking failed: {e}")
    
    try:
        medical_data = torch.stack([s['medical_data'] for s in samples])
        print(f"✓ Medical data stacked: {medical_data.shape}")
    except Exception as e:
        print(f"✗ Medical data stacking failed: {e}")
    
    try:
        image_paths = [s['image_path'] for s in samples]
        print(f"✓ Image paths collected: {len(image_paths)}")
    except Exception as e:
        print(f"✗ Image paths collection failed: {e}")
    
    try:
        medical_records = [s['medical_record'] for s in samples]
        print(f"✓ Medical records collected: {len(medical_records)}")
        
        # Check if medical records have consistent structure
        first_keys = set(medical_records[0].keys())
        for i, record in enumerate(medical_records[1:], 1):
            if set(record.keys()) != first_keys:
                print(f"✗ Medical record {i} has different keys: {set(record.keys())} vs {first_keys}")
            else:
                print(f"✓ Medical record {i} has consistent keys")
                
        # Check list lengths in medical records
        for key in first_keys:
            values = [record[key] for record in medical_records]
            types = [type(v) for v in values]
            if len(set(types)) > 1:
                print(f"✗ Medical record field '{key}' has inconsistent types: {types}")
            elif isinstance(values[0], list):
                lengths = [len(v) for v in values]
                if len(set(lengths)) > 1:
                    print(f"✗ Medical record field '{key}' has inconsistent list lengths: {lengths}")
                else:
                    print(f"✓ Medical record field '{key}' has consistent list length: {lengths[0]}")
            else:
                print(f"✓ Medical record field '{key}' is not a list")
                
    except Exception as e:
        print(f"✗ Medical records collection failed: {e}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

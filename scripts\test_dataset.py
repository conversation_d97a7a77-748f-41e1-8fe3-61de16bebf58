#!/usr/bin/env python3
"""
Test script to debug dataset loading issues.
"""

import sys
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from data.dataset import SteganoDataset

def test_dataset_loading():
    """Test dataset loading with debug information."""
    print("🔍 Testing dataset loading...")
    
    # Test parameters
    image_dir = "data/medical_images"
    medical_data_path = "data/unified_medical_real/unified_real_train_records.json"
    
    print(f"📁 Image directory: {image_dir}")
    print(f"📊 Medical data path: {medical_data_path}")
    
    # Check if paths exist
    image_path = Path(image_dir)
    medical_path = Path(medical_data_path)
    
    print(f"📁 Image directory exists: {image_path.exists()}")
    print(f"📊 Medical data file exists: {medical_path.exists()}")
    
    if image_path.exists():
        print(f"📁 Image directory contents:")
        for item in image_path.iterdir():
            if item.is_dir():
                print(f"   📂 {item.name}/")
                # Check subdirectories
                for subitem in item.iterdir():
                    if subitem.is_dir():
                        print(f"      📂 {subitem.name}/")
                        # Count files in subdirectory
                        files = list(subitem.glob("*.png"))
                        print(f"         📄 {len(files)} PNG files")
    
    try:
        # Create dataset
        print("\n🏗️  Creating dataset...")
        dataset = SteganoDataset(
            image_dir=image_dir,
            medical_data_path=medical_data_path,
            image_size=(256, 256),
            max_data_length=1024,
            use_synthetic_data=False,  # Use real unified data
            medical_data_ratio=1.0,  # Use medical data for all samples
        )
        
        print(f"✅ Dataset created successfully!")
        print(f"📊 Dataset size: {len(dataset)}")
        
        if len(dataset) > 0:
            print("\n🧪 Testing first sample...")
            sample = dataset[0]
            print(f"   📸 Image shape: {sample['image'].shape}")
            print(f"   📋 Medical data shape: {sample['medical_data'].shape}")
            print(f"   📁 Image path: {sample['image_path']}")
            print(f"   👤 Patient ID: {sample['medical_record']['patient_id']}")
            
            return True
        else:
            print("❌ Dataset is empty!")
            return False
            
    except Exception as e:
        print(f"❌ Error creating dataset: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🧪 Dataset Loading Test")
    print("=" * 50)
    
    success = test_dataset_loading()
    
    if success:
        print("\n🎉 Dataset loading test passed!")
        return 0
    else:
        print("\n💥 Dataset loading test failed!")
        return 1

if __name__ == "__main__":
    exit(main())

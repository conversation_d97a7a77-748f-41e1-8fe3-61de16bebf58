# SteganoGAN Comprehensive Demo

This comprehensive demo script tests the SteganoGAN medical data transmission system using the unified dataset (containing both real MIMIC-IV and synthetic medical records) across all available medical image types and resolutions.

## Features

- **Multi-Modal Testing**: Tests across all medical image types (chest X-ray, CT, MRI, ultrasound)
- **Unified Dataset**: Uses both authentic MIMIC-IV clinical records and synthetic medical data
- **Comprehensive Metrics**: Generates PSNR, SSIM, extraction accuracy, and medical data integrity metrics
- **Visual Comparisons**: Creates side-by-side comparisons of original vs steganographic images
- **Statistical Analysis**: Provides performance distributions and cross-modality comparisons
- **Automated Reporting**: Generates detailed reports for PhD research documentation

## Quick Start

### 1. Test the Demo Setup
```bash
# Test that everything is working
python scripts/test_comprehensive_demo.py
```

### 2. Run Basic Demo
```bash
# Run with default settings (10 records, 5 images per modality)
python scripts/comprehensive_demo.py
```

### 3. Run Custom Demo
```bash
# Run with custom parameters
python scripts/comprehensive_demo.py \
    --num_records 15 \
    --num_images_per_modality 8 \
    --device cuda \
    --output my_demo_results
```

## Command Line Options

| Option | Default | Description |
|--------|---------|-------------|
| `--dataset` | `data/unified_medical_real/unified_real_sample_records.json` | Path to unified medical dataset |
| `--checkpoint` | `None` | Path to trained model checkpoint |
| `--config` | `default` | Model configuration to use |
| `--device` | `cuda` | Device to use (cuda/cpu) |
| `--output` | `comprehensive_demo_results` | Output directory for results |
| `--num_records` | `10` | Number of medical records to test |
| `--num_images_per_modality` | `5` | Number of images per modality to test |
| `--generate_report` | `False` | Generate comprehensive PDF report |

## Output Structure

The demo generates the following outputs:

```
comprehensive_demo_results/
├── comprehensive_demo_results.json          # Complete numerical results
├── COMPREHENSIVE_DEMO_REPORT.md            # Human-readable summary
└── visualizations/
    ├── modality_performance_comparison.png  # Performance charts
    ├── metric_distributions.png             # Statistical distributions
    └── sample_steganographic_comparisons.png # Visual examples
```

## Example Usage Scenarios

### PhD Research Validation
```bash
# Comprehensive testing for research documentation
python scripts/comprehensive_demo.py \
    --num_records 20 \
    --num_images_per_modality 10 \
    --generate_report \
    --output phd_validation_results
```

### Quick Performance Check
```bash
# Fast testing with minimal data
python scripts/comprehensive_demo.py \
    --num_records 5 \
    --num_images_per_modality 2 \
    --device cpu \
    --output quick_test
```

### Model Comparison
```bash
# Test with trained model checkpoint
python scripts/comprehensive_demo.py \
    --checkpoint models/steganogan_best.pth \
    --num_records 15 \
    --output trained_model_results
```

## Understanding the Results

### Key Metrics

1. **PSNR (Peak Signal-to-Noise Ratio)**: Higher values indicate better image quality preservation
2. **SSIM (Structural Similarity Index)**: Values closer to 1 indicate better structural preservation
3. **Extraction Accuracy**: Percentage of correctly extracted medical data
4. **Success Rate**: Percentage of successful steganographic operations

### Performance Interpretation

- **PSNR > 30 dB**: Excellent image quality
- **SSIM > 0.9**: Very good structural similarity
- **Extraction Accuracy > 95%**: Reliable data transmission
- **Success Rate > 90%**: Robust system performance

## Medical Image Modalities Tested

1. **Chest X-ray**: Standard radiographic images
2. **CT Scan**: Computed tomography slices
3. **MRI**: Magnetic resonance imaging
4. **Ultrasound**: Ultrasonic medical imaging

All images are tested at 256x256 resolution for consistency.

## Dataset Information

The demo uses the unified medical dataset that combines:
- **MIMIC-IV Demo Records**: Real clinical data from the MIMIC-IV database
- **Synthetic Medical Records**: Generated medical data with realistic clinical parameters

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Use `--device cpu` or reduce `--num_records`
2. **Missing Images**: Check that medical images are generated in `data/medical_images/`
3. **Dataset Not Found**: Ensure unified dataset exists at the specified path

### Performance Tips

- Use GPU (`--device cuda`) for faster processing
- Start with small numbers for initial testing
- Monitor memory usage with large datasets

## Integration with PhD Research

This demo is specifically designed for PhD research validation:

1. **Reproducible Results**: All parameters and random seeds are logged
2. **Statistical Significance**: Multiple tests provide confidence intervals
3. **Visual Documentation**: Generated plots are publication-ready
4. **Comprehensive Metrics**: Covers all aspects needed for academic evaluation

## Next Steps

After running the comprehensive demo:

1. Review the generated report (`COMPREHENSIVE_DEMO_REPORT.md`)
2. Analyze the performance visualizations
3. Use results for PhD thesis documentation
4. Compare performance across different model configurations
5. Validate system readiness for clinical deployment

For questions or issues, refer to the main project documentation or contact the development team.

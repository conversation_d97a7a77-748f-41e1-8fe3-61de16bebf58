{"mimic_analysis": {"analysis_timestamp": "2025-05-26T07:11:21.609903", "mimic_path": "mimic-iv-clinical-database-demo-2.2", "tables": {"patients": {"table_name": "patients", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\patients.csv", "total_rows_analyzed": 100, "columns": ["subject_id", "gender", "anchor_age", "anchor_year", "anchor_year_group", "dod"], "column_count": 6, "data_types": {"subject_id": "int64", "gender": "object", "anchor_age": "int64", "anchor_year": "int64", "anchor_year_group": "object", "dod": "object"}, "sample_data": {"subject_id": [10014729, 10003400, 10002428], "gender": ["F", "F", "F"], "anchor_age": [21, 72, 80], "anchor_year": [2125, 2134, 2155], "anchor_year_group": ["2011 - 2013", "2011 - 2013", "2011 - 2013"], "dod": ["2137-09-02", "2143-03-30", "2146-02-09"]}, "null_counts": {"subject_id": 0, "gender": 0, "anchor_age": 0, "anchor_year": 0, "anchor_year_group": 0, "dod": 69}, "unique_counts": {"subject_id": 100, "gender": 2, "anchor_age": 50, "anchor_year": 62, "anchor_year_group": 2, "dod": 31}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018776.86}, "anchor_age": {"min": 21.0, "max": 91.0, "mean": 61.75}, "anchor_year": {"min": 2110.0, "max": 2201.0, "mean": 2148.68}}}, "admissions": {"table_name": "admissions", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\admissions.csv", "total_rows_analyzed": 200, "columns": ["subject_id", "hadm_id", "admittime", "dischtime", "deathtime", "admission_type", "admit_provider_id", "admission_location", "discharge_location", "insurance", "language", "marital_status", "race", "edregtime", "edouttime", "hospital_expire_flag"], "column_count": 16, "data_types": {"subject_id": "int64", "hadm_id": "int64", "admittime": "object", "dischtime": "object", "deathtime": "object", "admission_type": "object", "admit_provider_id": "object", "admission_location": "object", "discharge_location": "object", "insurance": "object", "language": "object", "marital_status": "object", "race": "object", "edregtime": "object", "edouttime": "object", "hospital_expire_flag": "int64"}, "sample_data": {"subject_id": [10004235, 10009628, 10018081], "hadm_id": [24181354, 25926192, 23983182], "admittime": ["2196-02-24 14:38:00", "2153-09-17 17:08:00", "2134-08-18 02:02:00"], "dischtime": ["2196-03-04 14:02:00", "2153-09-25 13:20:00", "2134-08-23 19:35:00"], "deathtime": ["2111-11-15 17:20:00", "2115-10-12 22:20:00", "2177-03-29 14:15:00"], "admission_type": ["URGENT", "URGENT", "URGENT"], "admit_provider_id": ["P03YMR", "P41R5N", "P233F6"], "admission_location": ["TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL", "TRANSFER FROM HOSPITAL"], "discharge_location": ["SKILLED NURSING FACILITY", "HOME HEALTH CARE", "SKILLED NURSING FACILITY"], "insurance": ["Medicaid", "Medicaid", "Medicare"], "language": ["ENGLISH", "?", "ENGLISH"], "marital_status": ["SINGLE", "MARRIED", "MARRIED"], "race": ["BLACK/CAPE VERDEAN", "HISPANIC/LATINO - PUERTO RICAN", "WHITE"], "edregtime": ["2196-02-24 12:15:00", "2134-08-17 16:24:00", "2119-10-26 06:00:00"], "edouttime": ["2196-02-24 17:07:00", "2134-08-18 03:15:00", "2119-10-26 06:37:00"], "hospital_expire_flag": [0, 0, 0]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "admittime": 0, "dischtime": 0, "deathtime": 188, "admission_type": 0, "admit_provider_id": 0, "admission_location": 0, "discharge_location": 30, "insurance": 0, "language": 0, "marital_status": 12, "race": 0, "edregtime": 60, "edouttime": 60, "hospital_expire_flag": 0}, "unique_counts": {"subject_id": 90, "hadm_id": 200, "admittime": 200, "dischtime": 200, "deathtime": 12, "admission_type": 5, "admit_provider_id": 136, "admission_location": 10, "discharge_location": 10, "insurance": 3, "language": 2, "marital_status": 4, "race": 14, "edregtime": 139, "edouttime": 139, "hospital_expire_flag": 2}, "value_ranges": {"subject_id": {"min": 10000032.0, "max": 10040025.0, "mean": 10018342.725}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25134623.755}, "hospital_expire_flag": {"min": 0.0, "max": 1.0, "mean": 0.06}}}, "diagnoses_icd": {"table_name": "diagnoses_icd", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\diagnoses_icd.csv", "total_rows_analyzed": 200, "columns": ["subject_id", "hadm_id", "seq_num", "icd_code", "icd_version"], "column_count": 5, "data_types": {"subject_id": "int64", "hadm_id": "int64", "seq_num": "int64", "icd_code": "object", "icd_version": "int64"}, "sample_data": {"subject_id": [10035185, 10035185, 10035185], "hadm_id": [22580999, 22580999, 22580999], "seq_num": [3, 10, 1], "icd_code": ["4139", "V707", "41401"], "icd_version": [9, 9, 9]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "seq_num": 0, "icd_code": 0, "icd_version": 0}, "unique_counts": {"subject_id": 21, "hadm_id": 22, "seq_num": 21, "icd_code": 161, "icd_version": 2}, "value_ranges": {"subject_id": {"min": 10001217.0, "max": 10038933.0, "mean": 10019333.79}, "hadm_id": {"min": 20044587.0, "max": 29374560.0, "mean": 24867555.045}, "seq_num": {"min": 1.0, "max": 21.0, "mean": 6.1}, "icd_version": {"min": 9.0, "max": 10.0, "mean": 9.175}}}, "prescriptions": {"table_name": "prescriptions", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\prescriptions.csv", "total_rows_analyzed": 200, "columns": ["subject_id", "hadm_id", "pharmacy_id", "poe_id", "poe_seq", "order_provider_id", "starttime", "stoptime", "drug_type", "drug", "formulary_drug_cd", "gsn", "ndc", "prod_strength", "form_rx", "dose_val_rx", "dose_unit_rx", "form_val_disp", "form_unit_disp", "doses_per_24_hrs", "route"], "column_count": 21, "data_types": {"subject_id": "int64", "hadm_id": "int64", "pharmacy_id": "int64", "poe_id": "object", "poe_seq": "float64", "order_provider_id": "object", "starttime": "object", "stoptime": "object", "drug_type": "object", "drug": "object", "formulary_drug_cd": "object", "gsn": "object", "ndc": "float64", "prod_strength": "object", "form_rx": "float64", "dose_val_rx": "float64", "dose_unit_rx": "object", "form_val_disp": "float64", "form_unit_disp": "object", "doses_per_24_hrs": "float64", "route": "object"}, "sample_data": {"subject_id": [10027602, 10027602, 10027602], "hadm_id": [28166872, 28166872, 28166872], "pharmacy_id": [27168639, 40720238, 62845687], "poe_id": ["10023239-119", "10023239-284", "10023239-308"], "poe_seq": [119.0, 284.0, 308.0], "order_provider_id": ["P45MRP", "P820BX", "P26UJY"], "starttime": ["2201-10-30 12:00:00", "2201-10-30 12:00:00", "2201-10-31 12:00:00"], "stoptime": ["2137-06-22 19:00:00", "2140-10-06 16:00:00", "2140-10-08 19:00:00"], "drug_type": ["MAIN", "MAIN", "MAIN"], "drug": ["Fentanyl Citrate", "Fentanyl Citrate", "Lorazepam"], "formulary_drug_cd": ["FENT2I", "FENT2I", "LORA2I"], "gsn": ["027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 ", "027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 ", "027413 001723 029916 016311 001750 001740 016312 001757 001752 001753 027414 001744 001743 001730 001726 019179 "], "ndc": [2821501.0, 2821501.0, 2821501.0], "prod_strength": ["100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial", "100 Units / mL - 10 mL Vial"], "form_rx": [], "dose_val_rx": [0.0, 0.0, 0.0], "dose_unit_rx": ["UNIT", "UNIT", "UNIT"], "form_val_disp": [0.0, 0.0, 0.0], "form_unit_disp": ["mL", "mL", "mL"], "doses_per_24_hrs": [1.0, 0.0, 1.0], "route": ["SC", "SC", "SC"]}, "null_counts": {"subject_id": 0, "hadm_id": 0, "pharmacy_id": 0, "poe_id": 6, "poe_seq": 6, "order_provider_id": 8, "starttime": 0, "stoptime": 6, "drug_type": 0, "drug": 0, "formulary_drug_cd": 0, "gsn": 6, "ndc": 9, "prod_strength": 9, "form_rx": 200, "dose_val_rx": 9, "dose_unit_rx": 9, "form_val_disp": 9, "form_unit_disp": 9, "doses_per_24_hrs": 197, "route": 6}, "unique_counts": {"subject_id": 51, "hadm_id": 75, "pharmacy_id": 200, "poe_id": 193, "poe_seq": 170, "order_provider_id": 115, "starttime": 192, "stoptime": 171, "drug_type": 1, "drug": 8, "formulary_drug_cd": 11, "gsn": 6, "ndc": 6, "prod_strength": 4, "form_rx": 0, "dose_val_rx": 1, "dose_unit_rx": 3, "form_val_disp": 1, "form_unit_disp": 2, "doses_per_24_hrs": 2, "route": 3}, "value_ranges": {"subject_id": {"min": 10002428.0, "max": 10040025.0, "mean": 10020410.355}, "hadm_id": {"min": 20044587.0, "max": 29974575.0, "mean": 25269255.44}, "pharmacy_id": {"min": 408384.0, "max": 99787861.0, "mean": 48723640.22}, "poe_seq": {"min": 17.0, "max": 1436.0, "mean": 407.50515463917526}, "ndc": {"min": 0.0, "max": 63323031119.0, "mean": 592473625.4712042}, "dose_val_rx": {"min": 0.0, "max": 0.0, "mean": 0.0}, "form_val_disp": {"min": 0.0, "max": 0.0, "mean": 0.0}, "doses_per_24_hrs": {"min": 0.0, "max": 1.0, "mean": 0.6666666666666666}}}, "labevents": {"table_name": "labevents", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\labevents.csv", "total_rows_analyzed": 200, "columns": ["labevent_id", "subject_id", "hadm_id", "specimen_id", "itemid", "order_provider_id", "charttime", "storetime", "value", "valuenum", "valueuom", "ref_range_lower", "ref_range_upper", "flag", "priority", "comments"], "column_count": 16, "data_types": {"labevent_id": "int64", "subject_id": "int64", "hadm_id": "float64", "specimen_id": "int64", "itemid": "int64", "order_provider_id": "float64", "charttime": "object", "storetime": "object", "value": "object", "valuenum": "float64", "valueuom": "object", "ref_range_lower": "float64", "ref_range_upper": "float64", "flag": "object", "priority": "object", "comments": "object"}, "sample_data": {"labevent_id": [172061, 172062, 172068], "subject_id": [10014354, 10014354, 10014354], "hadm_id": [29600294.0, 29600294.0, 29600294.0], "specimen_id": [1808066, 1808066, 1808066], "itemid": [51277, 51279, 52172], "order_provider_id": [], "charttime": ["2148-08-16 00:00:00", "2148-08-16 00:00:00", "2148-08-16 00:00:00"], "storetime": ["2148-08-16 01:30:00", "2148-08-16 01:30:00", "2148-08-16 01:30:00"], "value": ["15.4", "3.35", "49.7"], "valuenum": [15.4, 3.35, 49.7], "valueuom": ["%", "m/uL", "fL"], "ref_range_lower": [10.5, 4.6, 35.1], "ref_range_upper": [15.5, 6.1, 46.3], "flag": ["abnormal", "abnormal", "abnormal"], "priority": ["ROUTINE", "ROUTINE", "ROUTINE"], "comments": ["New reference range as of ___.", "Using this patient's age, gender, and serum creatinine value of 1.8, .  estimated GFR (eGFR) is likely between 39 and 47 mL/min/1.73 m2, .  provided the serum creatinine value is stable. .  (Patients with more muscle mass and better nutritional status are more .  likely to be at the higher end of this range.) .  An eGFR < 60 suggests kidney disease in those below the age of 65 .  and there may be kidney disease in those over 65..", "New reference range as of ___."]}, "null_counts": {"labevent_id": 0, "subject_id": 0, "hadm_id": 8, "specimen_id": 0, "itemid": 0, "order_provider_id": 200, "charttime": 0, "storetime": 0, "value": 24, "valuenum": 24, "valueuom": 31, "ref_range_lower": 29, "ref_range_upper": 29, "flag": 128, "priority": 0, "comments": 164}, "unique_counts": {"labevent_id": 200, "subject_id": 3, "hadm_id": 4, "specimen_id": 12, "itemid": 60, "order_provider_id": 0, "charttime": 6, "storetime": 18, "value": 122, "valuenum": 126, "valueuom": 10, "ref_range_lower": 45, "ref_range_upper": 49, "flag": 1, "priority": 2, "comments": 11}, "value_ranges": {"labevent_id": {"min": 172038.0, "max": 411740.0, "mean": 277546.245}, "subject_id": {"min": 10014354.0, "max": 10035631.0, "mean": 10023789.155}, "hadm_id": {"min": 20385771.0, "max": 29600294.0, "mean": 26852186.427083332}, "specimen_id": {"min": 1808066.0, "max": 95196756.0, "mean": 39999477.3}, "itemid": {"min": 50861.0, "max": 52172.0, "mean": 51145.99}, "valuenum": {"min": 0.0, "max": 468.0, "mean": 39.97534090909091}, "ref_range_lower": {"min": 0.0, "max": 180.0, "mean": 25.982456140350877}, "ref_range_upper": {"min": 0.0, "max": 440.0, "mean": 48.1540350877193}}}, "d_icd_diagnoses": {"table_name": "d_icd_diagnoses", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_icd_diagnoses.csv", "total_rows_analyzed": 200, "columns": ["icd_code", "icd_version", "long_title"], "column_count": 3, "data_types": {"icd_code": "int64", "icd_version": "int64", "long_title": "object"}, "sample_data": {"icd_code": [90, 1160, 1186], "icd_version": [9, 9, 9], "long_title": ["Infectious colitis, enteritis, and gastroenteritis", "Tuberculous pneumonia [any form], unspecified", "Other specified pulmonary tuberculosis, tubercle bacilli not found by bacteriological or histological examination, but tuberculosis confirmed by other methods [inoculation of animals]"]}, "null_counts": {"icd_code": 0, "icd_version": 0, "long_title": 0}, "unique_counts": {"icd_code": 200, "icd_version": 1, "long_title": 200}, "value_ranges": {"icd_code": {"min": 90.0, "max": 80433.0, "mean": 33191.665}, "icd_version": {"min": 9.0, "max": 9.0, "mean": 9.0}}}, "d_labitems": {"table_name": "d_labitems", "file_path": "mimic-iv-clinical-database-demo-2.2\\hosp\\d_labitems.csv", "total_rows_analyzed": 200, "columns": ["itemid", "label", "fluid", "category"], "column_count": 4, "data_types": {"itemid": "int64", "label": "object", "fluid": "object", "category": "object"}, "sample_data": {"itemid": [50808, 50826, 50813], "label": ["Free Calcium", "Tidal Volume", "Lactate"], "fluid": ["Blood", "Blood", "Blood"], "category": ["Blood Gas", "Blood Gas", "Blood Gas"]}, "null_counts": {"itemid": 0, "label": 0, "fluid": 0, "category": 0}, "unique_counts": {"itemid": 200, "label": 183, "fluid": 4, "category": 2}, "value_ranges": {"itemid": {"min": 50801.0, "max": 53153.0, "mean": 51449.84}}}}, "summary": {"total_patients_analyzed": 100, "total_records_analyzed": 1300, "tables_found": 7, "tables_missing": 0}}, "synthetic_comparison": {"synthetic_data_path": "data\\medical_samples\\compact_sample_records.json", "synthetic_record_count": 20, "synthetic_structure": {"id": "P1215", "age": 62, "gender": "M", "dx": [{"icd": "R06.02", "seq": 1}, {"icd": "R50.9", "seq": 2}], "rx": [{"drug": "Lisinopril", "dose": "98mg", "freq": "QD"}], "labs": [{"id": "50912", "val": 2.2, "unit": "mg/dL", "flag": "H"}, {"id": "50931", "val": 102.0, "unit": "mg/dL", "flag": null}], "size": 261, "timestamp": "2025-05-26T06:11:11", "type": "compact_medical"}, "field_mappings": {"patients": {"subject_id": "patient_id", "gender": "gender", "anchor_age": "age"}, "diagnoses": {"icd_code": "icd_code", "seq_num": "seq_num"}, "prescriptions": {"drug": "drug", "dose_val_rx": "dose_val", "dose_unit_rx": "dose_unit"}, "labevents": {"itemid": "item_id", "value": "value", "valuenum": "value_num", "valueuom": "unit", "flag": "flag"}}, "integration_plan": {}, "compatibility_issues": [], "recommendations": ["✅ MIMIC-IV structure is compatible with synthetic data format", "🔄 Field name mapping required for seamless integration", "📏 Size filtering needed to meet steganographic constraints", "🔗 Patient linking across tables using subject_id/hadm_id", "⚡ Preprocessing pipeline can handle both data sources"]}}
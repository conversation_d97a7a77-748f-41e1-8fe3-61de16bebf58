"""
Training configuration for SteganoGAN medical data steganography.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple


@dataclass
class OptimizerConfig:
    """Configuration for optimizers."""

    # Generator optimizer
    generator_lr: float = 2e-4
    generator_betas: Tuple[float, float] = (0.5, 0.999)
    generator_weight_decay: float = 1e-4

    # Discriminator optimizer
    discriminator_lr: float = 2e-4
    discriminator_betas: Tuple[float, float] = (0.5, 0.999)
    discriminator_weight_decay: float = 1e-4

    # Decoder optimizer
    decoder_lr: float = 1e-3
    decoder_betas: Tuple[float, float] = (0.9, 0.999)
    decoder_weight_decay: float = 1e-4

    # Learning rate scheduling
    use_scheduler: bool = True
    scheduler_type: str = "cosine"  # "cosine", "step", "plateau"
    scheduler_patience: int = 10
    scheduler_factor: float = 0.5


@dataclass
class LossConfig:
    """Configuration for loss functions."""

    # Loss weights
    adversarial_weight: float = 1.0  # GAN loss weight
    reconstruction_weight: float = 10.0  # Image reconstruction loss
    perceptual_weight: float = 1.0  # Perceptual loss weight
    decoder_weight: float = 5.0  # Data extraction loss weight

    # Specific loss parameters
    perceptual_layers: Optional[List[str]] = field(default_factory=lambda: ['relu1_2', 'relu2_2', 'relu3_3', 'relu4_3'])
    use_feature_matching: bool = True  # Feature matching loss
    feature_matching_weight: float = 10.0

    # Medical data specific losses
    medical_data_weight: float = 20.0  # Higher weight for medical data accuracy
    use_semantic_loss: bool = True  # Semantic consistency for medical data


@dataclass
class DataConfig:
    """Configuration for data handling."""

    # Dataset parameters
    dataset_path: str = "data/raw"
    medical_data_path: str = "data/medical_samples"
    batch_size: int = 16
    num_workers: int = 4
    pin_memory: bool = True

    # Data augmentation
    use_augmentation: bool = True
    augmentation_prob: float = 0.5
    horizontal_flip: bool = True
    rotation_range: int = 10
    brightness_range: float = 0.1
    contrast_range: float = 0.1

    # Data splitting
    train_split: float = 0.8
    val_split: float = 0.1
    test_split: float = 0.1

    # Medical data handling
    medical_data_formats: List[str] = field(default_factory=lambda: ["json", "txt", "csv"])
    max_medical_file_size: int = 1024  # bytes
    anonymize_data: bool = True  # Remove patient identifiers
    medical_data_ratio: float = 0.3  # Ratio of samples with medical data

    def __post_init__(self):
        assert abs(self.train_split + self.val_split + self.test_split - 1.0) < 1e-6


@dataclass
class TrainingConfig:
    """Main training configuration."""

    # Sub-configurations
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    loss: LossConfig = field(default_factory=LossConfig)
    data: DataConfig = field(default_factory=DataConfig)

    # Training parameters
    num_epochs: int = 200
    save_every: int = 10  # Save checkpoint every N epochs
    validate_every: int = 5  # Validate every N epochs
    log_every: int = 100  # Log every N steps

    # Early stopping
    use_early_stopping: bool = True
    early_stopping_patience: int = 20
    early_stopping_metric: str = "val_loss"
    early_stopping_mode: str = "min"  # "min" or "max"

    # Checkpointing
    checkpoint_dir: str = "models"
    save_best_only: bool = False
    save_top_k: int = 3

    # Logging and monitoring
    log_dir: str = "results/logs"
    use_tensorboard: bool = True
    use_wandb: bool = False  # Set to True if using Weights & Biases
    wandb_project: str = "medical-steganogan"

    # Evaluation
    eval_metrics: List[str] = field(default_factory=lambda: [
        "psnr", "ssim", "lpips", "fid", "extraction_accuracy"
    ])

    # Progressive training
    use_progressive_training: bool = True
    progressive_epochs: List[int] = field(default_factory=lambda: [50, 100, 150])  # Epochs to increase complexity
    progressive_resolutions: List[Tuple[int, int]] = field(default_factory=lambda: [
        (64, 64), (128, 128), (256, 256)
    ])

    # Medical data specific training
    medical_data_ratio: float = 0.3  # Ratio of samples with medical data
    simulate_medical_scenarios: bool = True  # Simulate real medical use cases

    # Security and privacy
    differential_privacy: bool = False  # Enable differential privacy
    privacy_epsilon: float = 1.0  # Privacy budget
    secure_aggregation: bool = False  # For federated learning scenarios

    # Hardware and performance
    device: str = "cuda"
    mixed_precision: bool = True
    gradient_clip_val: float = 1.0
    accumulate_grad_batches: int = 1

    # Reproducibility
    seed: int = 42
    deterministic: bool = True

    def __post_init__(self):
        """Validate training configuration."""
        assert self.num_epochs > 0
        assert self.save_every > 0
        assert self.validate_every > 0
        assert 0 < self.medical_data_ratio <= 1.0

        if self.use_progressive_training:
            assert len(self.progressive_epochs) == len(self.progressive_resolutions)


# Default configuration instance
DEFAULT_TRAINING_CONFIG = TrainingConfig()


def get_training_config(config_name: str = "default") -> TrainingConfig:
    """
    Get training configuration by name.

    Args:
        config_name: Name of the configuration

    Returns:
        TrainingConfig instance
    """
    if config_name == "default":
        return DEFAULT_TRAINING_CONFIG
    elif config_name == "fast":
        # Fast training configuration for development
        config = TrainingConfig()
        config.num_epochs = 50
        config.data.batch_size = 32
        config.save_every = 5
        config.validate_every = 2
        config.use_progressive_training = False
        return config
    elif config_name == "production":
        # Production configuration for final training
        config = TrainingConfig()
        config.num_epochs = 500
        config.data.batch_size = 8
        config.optimizer.generator_lr = 1e-4
        config.optimizer.discriminator_lr = 1e-4
        config.loss.reconstruction_weight = 20.0
        config.loss.medical_data_weight = 50.0
        return config
    elif config_name == "mimic":
        # MIMIC-IV specific configuration
        config = TrainingConfig()
        config.num_epochs = 300
        config.data.batch_size = 12
        config.data.dataset_path = "data/raw"
        config.data.medical_data_path = "data/mimic_processed/train_records.json"
        config.optimizer.generator_lr = 1.5e-4
        config.optimizer.discriminator_lr = 1.5e-4
        config.loss.reconstruction_weight = 15.0
        config.loss.medical_data_weight = 30.0
        config.loss.perceptual_weight = 2.0
        config.data.medical_data_ratio = 1.0  # Use medical data for all samples
        config.data.anonymize_data = True
        config.use_progressive_training = True
        config.progressive_epochs = [75, 150, 225]
        config.progressive_resolutions = [(128, 128), (192, 192), (256, 256)]
        return config
    elif config_name == "mimic_fast":
        # Fast MIMIC-IV configuration for testing
        config = TrainingConfig()
        config.num_epochs = 100
        config.data.batch_size = 16
        config.data.dataset_path = "data/raw"
        config.data.medical_data_path = "data/mimic_processed/sample_records.json"
        config.optimizer.generator_lr = 2e-4
        config.optimizer.discriminator_lr = 2e-4
        config.loss.medical_data_weight = 25.0
        config.data.medical_data_ratio = 1.0
        config.use_progressive_training = False
        return config
    elif config_name == "unified_real":
        # Configuration for unified real dataset (MIMIC-IV + synthetic)
        config = TrainingConfig()
        config.num_epochs = 250
        config.data.batch_size = 16
        config.data.dataset_path = "data/medical_images"
        config.data.medical_data_path = "data/unified_medical_real/unified_real_train_records.json"
        config.optimizer.generator_lr = 1e-4
        config.optimizer.discriminator_lr = 1e-4
        config.optimizer.decoder_lr = 5e-4
        config.loss.reconstruction_weight = 15.0
        config.loss.medical_data_weight = 25.0
        config.loss.perceptual_weight = 2.0
        config.loss.feature_matching_weight = 8.0
        config.data.medical_data_ratio = 1.0  # Use medical data for all samples
        config.data.anonymize_data = True
        config.use_progressive_training = True
        config.progressive_epochs = [60, 120, 180]
        config.progressive_resolutions = [(128, 128), (192, 192), (256, 256)]
        config.save_every = 10
        config.validate_every = 5
        config.log_every = 50
        config.checkpoint_dir = "models/unified_real"
        config.log_dir = "results/logs/unified_real"
        return config
    elif config_name == "unified_real_fast":
        # Fast training configuration for unified real dataset
        config = TrainingConfig()
        config.num_epochs = 100
        config.data.batch_size = 20
        config.data.dataset_path = "data/medical_images"
        config.data.medical_data_path = "data/unified_medical_real/unified_real_train_records.json"
        config.optimizer.generator_lr = 2e-4
        config.optimizer.discriminator_lr = 2e-4
        config.optimizer.decoder_lr = 8e-4
        config.loss.medical_data_weight = 20.0
        config.data.medical_data_ratio = 1.0
        config.use_progressive_training = False
        config.save_every = 5
        config.validate_every = 3
        config.checkpoint_dir = "models/unified_real_fast"
        config.log_dir = "results/logs/unified_real_fast"
        return config
    else:
        raise ValueError(f"Unknown configuration: {config_name}")


def save_training_config(config: TrainingConfig, filepath: str):
    """Save training configuration to file."""
    import json
    from dataclasses import asdict

    with open(filepath, 'w') as f:
        json.dump(asdict(config), f, indent=2)


def load_training_config(filepath: str) -> TrainingConfig:
    """Load training configuration from file."""
    import json

    with open(filepath, 'r') as f:
        config_dict = json.load(f)

    return TrainingConfig(**config_dict)

#!/usr/bin/env python3
"""
Verify synthetic medical data compatibility with SteganoGAN pipeline.
This script tests the generated synthetic data to ensure it works with the training system.
"""

import json
import sys
from pathlib import Path
import numpy as np

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from data.dataset import MedicalDataProcessor
    from data.mimic_processor import MIMICIVProcessor
except ImportError as e:
    print(f"Warning: Could not import data processors: {e}")
    print("This is expected if the full SteganoGAN system is not yet implemented.")


def verify_json_structure(file_path: str) -> dict:
    """Verify the structure and content of a JSON medical data file."""
    
    print(f"\n📋 Verifying: {file_path}")
    print("=" * 50)
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
    except Exception as e:
        return {"error": f"Failed to load JSON: {e}"}
    
    if not isinstance(data, list):
        return {"error": "Data should be a list of records"}
    
    if len(data) == 0:
        return {"error": "No records found"}
    
    # Analyze structure
    results = {
        "total_records": len(data),
        "sample_record": data[0],
        "record_sizes": [],
        "required_fields": [],
        "medical_content": {},
        "errors": []
    }
    
    # Check each record
    required_fields = ['id', 'age', 'gender', 'dx', 'rx', 'labs', 'size', 'type']
    
    for i, record in enumerate(data):
        if not isinstance(record, dict):
            results["errors"].append(f"Record {i} is not a dictionary")
            continue
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in record]
        if missing_fields:
            results["errors"].append(f"Record {i} missing fields: {missing_fields}")
        
        # Record size analysis
        if 'size' in record:
            results["record_sizes"].append(record['size'])
        
        # Medical content analysis
        if i == 0:  # Analyze first record for structure
            if 'dx' in record and isinstance(record['dx'], list):
                results["medical_content"]["diagnoses_example"] = record['dx']
            if 'rx' in record and isinstance(record['rx'], list):
                results["medical_content"]["prescriptions_example"] = record['rx']
            if 'labs' in record and isinstance(record['labs'], list):
                results["medical_content"]["labs_example"] = record['labs']
    
    # Calculate statistics
    if results["record_sizes"]:
        results["size_stats"] = {
            "min": min(results["record_sizes"]),
            "max": max(results["record_sizes"]),
            "mean": np.mean(results["record_sizes"]),
            "under_512": sum(1 for s in results["record_sizes"] if s <= 512),
            "under_1024": sum(1 for s in results["record_sizes"] if s <= 1024)
        }
    
    return results


def verify_steganography_compatibility(results: dict) -> dict:
    """Check if the data is suitable for steganographic embedding."""
    
    print(f"\n🔍 Steganography Compatibility Analysis")
    print("=" * 50)
    
    compatibility = {
        "suitable_for_embedding": True,
        "recommendations": [],
        "warnings": []
    }
    
    if "size_stats" in results:
        stats = results["size_stats"]
        total_records = results["total_records"]
        
        print(f"📊 Size Analysis:")
        print(f"   Average size: {stats['mean']:.0f} bytes")
        print(f"   Size range: {stats['min']}-{stats['max']} bytes")
        print(f"   Records ≤ 512 bytes: {stats['under_512']}/{total_records} ({100*stats['under_512']/total_records:.1f}%)")
        print(f"   Records ≤ 1024 bytes: {stats['under_1024']}/{total_records} ({100*stats['under_1024']/total_records:.1f}%)")
        
        # Compatibility checks
        if stats['mean'] > 1024:
            compatibility["suitable_for_embedding"] = False
            compatibility["warnings"].append("Average record size > 1024 bytes may be too large for steganography")
        
        if stats['max'] > 2048:
            compatibility["warnings"].append("Some records > 2048 bytes may not fit in standard images")
        
        if stats['under_1024'] / total_records < 0.8:
            compatibility["warnings"].append("Less than 80% of records are under 1024 bytes")
        
        if stats['under_512'] / total_records > 0.9:
            compatibility["recommendations"].append("Excellent: >90% of records under 512 bytes - optimal for steganography")
        elif stats['under_1024'] / total_records > 0.9:
            compatibility["recommendations"].append("Good: >90% of records under 1024 bytes - suitable for steganography")
    
    return compatibility


def verify_medical_content(results: dict) -> dict:
    """Verify the medical content quality and realism."""
    
    print(f"\n🏥 Medical Content Analysis")
    print("=" * 50)
    
    medical_quality = {
        "realistic": True,
        "issues": [],
        "strengths": []
    }
    
    if "medical_content" in results:
        content = results["medical_content"]
        
        # Check diagnoses
        if "diagnoses_example" in content:
            dx_example = content["diagnoses_example"]
            print(f"📋 Diagnoses example: {len(dx_example)} diagnoses")
            for dx in dx_example[:3]:  # Show first 3
                print(f"   - {dx.get('icd', 'N/A')} (seq: {dx.get('seq', 'N/A')})")
            
            if len(dx_example) > 0:
                medical_quality["strengths"].append("Contains ICD codes for diagnoses")
            
        # Check prescriptions
        if "prescriptions_example" in content:
            rx_example = content["prescriptions_example"]
            print(f"💊 Prescriptions example: {len(rx_example)} medications")
            for rx in rx_example[:3]:  # Show first 3
                print(f"   - {rx.get('drug', 'N/A')} {rx.get('dose', 'N/A')} {rx.get('freq', 'N/A')}")
            
            if len(rx_example) > 0:
                medical_quality["strengths"].append("Contains realistic medication data")
        
        # Check lab results
        if "labs_example" in content:
            labs_example = content["labs_example"]
            print(f"🧪 Lab results example: {len(labs_example)} lab values")
            for lab in labs_example[:3]:  # Show first 3
                print(f"   - {lab.get('id', 'N/A')}: {lab.get('val', 'N/A')} {lab.get('unit', 'N/A')}")
            
            if len(labs_example) > 0:
                medical_quality["strengths"].append("Contains lab values with units")
    
    return medical_quality


def test_data_loading() -> dict:
    """Test if the data can be loaded by the SteganoGAN data processors."""
    
    print(f"\n🔧 Data Loading Test")
    print("=" * 50)
    
    loading_test = {
        "processor_available": False,
        "loading_successful": False,
        "error_message": None
    }
    
    try:
        # Try to create a medical data processor
        processor = MedicalDataProcessor(max_data_length=1024)
        loading_test["processor_available"] = True
        print("✅ MedicalDataProcessor available")
        
        # Try to load a sample record
        sample_file = "data/medical_samples/compact_sample_records.json"
        if Path(sample_file).exists():
            with open(sample_file, 'r') as f:
                sample_data = json.load(f)
            
            if len(sample_data) > 0:
                # Try to process a record
                sample_record = sample_data[0]
                encoded = processor.encode_medical_record(sample_record)
                loading_test["loading_successful"] = True
                print(f"✅ Successfully encoded sample record to tensor shape: {encoded.shape}")
            else:
                loading_test["error_message"] = "No sample records found"
        else:
            loading_test["error_message"] = f"Sample file not found: {sample_file}"
            
    except ImportError:
        loading_test["error_message"] = "MedicalDataProcessor not available (expected during development)"
        print("⚠️  MedicalDataProcessor not available - this is expected during initial development")
    except Exception as e:
        loading_test["error_message"] = str(e)
        print(f"❌ Error testing data loading: {e}")
    
    return loading_test


def main():
    print("🔍 Synthetic Medical Data Verification")
    print("=" * 60)
    
    # Files to verify
    data_files = [
        "data/medical_samples/compact_sample_records.json",
        "data/medical_samples/compact_train_records.json",
        "data/medical_samples/synthetic_sample_records.json"
    ]
    
    all_results = {}
    
    for file_path in data_files:
        if Path(file_path).exists():
            results = verify_json_structure(file_path)
            
            if "error" not in results:
                # Additional analyses
                compatibility = verify_steganography_compatibility(results)
                medical_quality = verify_medical_content(results)
                
                all_results[file_path] = {
                    "structure": results,
                    "compatibility": compatibility,
                    "medical_quality": medical_quality
                }
                
                # Print summary
                print(f"\n✅ {Path(file_path).name}: PASSED")
                if compatibility["suitable_for_embedding"]:
                    print("   🎯 Suitable for steganographic embedding")
                if compatibility["recommendations"]:
                    for rec in compatibility["recommendations"]:
                        print(f"   💡 {rec}")
                if compatibility["warnings"]:
                    for warn in compatibility["warnings"]:
                        print(f"   ⚠️  {warn}")
            else:
                print(f"\n❌ {Path(file_path).name}: FAILED")
                print(f"   Error: {results['error']}")
                all_results[file_path] = {"error": results["error"]}
        else:
            print(f"\n⚠️  {file_path}: File not found")
    
    # Test data loading
    loading_results = test_data_loading()
    
    # Final summary
    print(f"\n📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    successful_files = [f for f, r in all_results.items() if "error" not in r]
    failed_files = [f for f, r in all_results.items() if "error" in r]
    
    print(f"✅ Successfully verified: {len(successful_files)} files")
    print(f"❌ Failed verification: {len(failed_files)} files")
    
    if successful_files:
        print(f"\n🎯 Ready for SteganoGAN training:")
        for file_path in successful_files:
            print(f"   - {Path(file_path).name}")
    
    if loading_results["loading_successful"]:
        print(f"\n🔧 Data loading test: ✅ PASSED")
    else:
        print(f"\n🔧 Data loading test: ⚠️  {loading_results['error_message']}")
    
    print(f"\n🚀 Next Steps:")
    print(f"1. Test with demo: python scripts/demo.py --medical_data data/medical_samples/compact_sample_records.json")
    print(f"2. Start training: python scripts/train.py --config mimic_fast")
    print(f"3. Monitor training progress and adjust parameters as needed")


if __name__ == "__main__":
    main()

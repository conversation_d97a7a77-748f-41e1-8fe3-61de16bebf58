"""
Model configuration for SteganoGAN medical data steganography.
"""

from dataclasses import dataclass, field
from typing import Tuple, List


@dataclass
class GeneratorConfig:
    """Configuration for the SteganoGAN Generator."""

    # Architecture parameters
    input_channels: int = 3  # RGB images
    output_channels: int = 3  # RGB output
    hidden_channels: int = 64  # Base number of channels
    num_blocks: int = 6  # Number of residual blocks
    use_attention: bool = True  # Use attention mechanisms

    # Data embedding parameters
    data_channels: int = 1  # Number of channels for hidden data
    embedding_dim: int = 32  # Dimension of data embedding
    max_data_length: int = 1024  # Maximum bytes of hidden data

    # Normalization and activation
    norm_type: str = "batch"  # "batch", "instance", or "layer"
    activation: str = "relu"  # "relu", "leaky_relu", or "gelu"
    dropout_rate: float = 0.1


@dataclass
class DiscriminatorConfig:
    """Configuration for the Discriminator network."""

    # Architecture parameters
    input_channels: int = 3  # RGB images
    hidden_channels: int = 64  # Base number of channels
    num_layers: int = 4  # Number of convolutional layers
    use_spectral_norm: bool = True  # Use spectral normalization

    # PatchGAN parameters
    patch_size: int = 70  # Size of discriminator patches

    # Normalization and activation
    norm_type: str = "batch"  # "batch", "instance", or "layer"
    activation: str = "leaky_relu"  # Activation function
    dropout_rate: float = 0.2


@dataclass
class DecoderConfig:
    """Configuration for the Data Decoder network."""

    # Architecture parameters
    input_channels: int = 3  # RGB images
    hidden_channels: int = 32  # Base number of channels
    num_layers: int = 3  # Number of convolutional layers

    # Data extraction parameters
    output_dim: int = 1024  # Maximum output data dimension
    use_attention: bool = False  # Use attention for data extraction

    # Normalization and activation
    norm_type: str = "batch"  # "batch", "instance", or "layer"
    activation: str = "relu"  # Activation function
    dropout_rate: float = 0.1


@dataclass
class ModelConfig:
    """Overall model configuration."""

    # Sub-model configurations
    generator: GeneratorConfig = field(default_factory=GeneratorConfig)
    discriminator: DiscriminatorConfig = field(default_factory=DiscriminatorConfig)
    decoder: DecoderConfig = field(default_factory=DecoderConfig)

    # Image specifications
    image_size: Tuple[int, int] = (256, 256)  # Height, Width
    image_channels: int = 3  # RGB

    # Medical data specifications
    medical_data_types: List[str] = field(default_factory=lambda: [
        "symptoms", "diagnosis", "lab_results", "patient_id"
    ])
    max_medical_data_size: int = 512  # Maximum bytes for medical data

    # Security parameters
    use_encryption: bool = True  # Enable encryption for hidden data
    key_length: int = 256  # Encryption key length in bits

    # Quality constraints
    max_psnr_degradation: float = 0.5  # Maximum PSNR degradation in dB
    min_ssim_score: float = 0.95  # Minimum SSIM score

    # Device and precision
    device: str = "cuda"  # "cuda" or "cpu"
    mixed_precision: bool = True  # Use mixed precision training

    def __post_init__(self):
        """Validate configuration parameters."""
        assert self.image_size[0] > 0 and self.image_size[1] > 0
        assert self.image_channels in [1, 3]
        assert self.max_medical_data_size <= self.generator.max_data_length
        assert 0 < self.max_psnr_degradation < 10
        assert 0 < self.min_ssim_score <= 1.0


# Default configuration instance
DEFAULT_MODEL_CONFIG = ModelConfig()


def get_model_config(config_name: str = "default") -> ModelConfig:
    """
    Get model configuration by name.

    Args:
        config_name: Name of the configuration

    Returns:
        ModelConfig instance
    """
    if config_name == "default":
        return DEFAULT_MODEL_CONFIG
    elif config_name == "lightweight":
        # Lightweight configuration for faster training/inference
        config = ModelConfig()
        config.generator.hidden_channels = 32
        config.generator.num_blocks = 4
        config.discriminator.hidden_channels = 32
        config.discriminator.num_layers = 3
        config.decoder.hidden_channels = 16
        return config
    elif config_name == "high_quality":
        # High quality configuration for better results
        config = ModelConfig()
        config.generator.hidden_channels = 128
        config.generator.num_blocks = 8
        config.discriminator.hidden_channels = 128
        config.discriminator.num_layers = 5
        config.decoder.hidden_channels = 64
        config.image_size = (512, 512)
        return config
    else:
        raise ValueError(f"Unknown configuration: {config_name}")


def save_config(config: ModelConfig, filepath: str):
    """Save configuration to file."""
    import json
    from dataclasses import asdict

    with open(filepath, 'w') as f:
        json.dump(asdict(config), f, indent=2)


def load_config(filepath: str) -> ModelConfig:
    """Load configuration from file."""
    import json

    with open(filepath, 'r') as f:
        config_dict = json.load(f)

    return ModelConfig(**config_dict)
